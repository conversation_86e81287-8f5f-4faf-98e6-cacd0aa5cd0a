package com.wosai.upay.job.enume.tonglianV2;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务开通申请主状态枚举
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum PayBizApplyStatusEnum {

    /**
     * 开通中
     */
    PROCESSING(1, "开通中", "开通中"),

    /**
     * 开通失败
     */
    FAILED(2, "开通失败", "开通失败"),

    /**
     * 开通成功
     */
    SUCCESS(3, "开通成功", "开通成功");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 前端展示文案
     */
    private final String displayText;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static PayBizApplyStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PayBizApplyStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为开通中状态
     *
     * @param code 状态码
     * @return true-开通中，false-非开通中
     */
    public static boolean isProcessing(Integer code) {
        return PROCESSING.getCode().equals(code);
    }

    /**
     * 判断是否为开通失败状态
     *
     * @param code 状态码
     * @return true-开通失败，false-非开通失败
     */
    public static boolean isFailed(Integer code) {
        return FAILED.getCode().equals(code);
    }

    /**
     * 判断是否为开通成功状态
     *
     * @param code 状态码
     * @return true-开通成功，false-非开通成功
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为终态（成功或失败）
     *
     * @param code 状态码
     * @return true-终态，false-非终态
     */
    public static boolean isFinalState(Integer code) {
        return isSuccess(code) || isFailed(code);
    }
}
