package com.wosai.upay.job.model.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 支付业务申请状态详情响应
 *
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayBizApplyStatusDetailRspDTO {

    /**
     * 状态
     */
    private Integer status;

    /**
     * 详细状态
     */
    private Integer detailStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 详细状态描述
     */
    private String detailStatusDesc;
    /**
     * 详情描述
     */
    private String description;

    /**
     * app文案
     */
    private String displayText;

    /**
     * 签约链接（只有在待签约状态返回）
     */
    private String signUrl;

    /**
     * 子商户号信息
     */
    private List<SubMchInfo> subMchInfo;

    /**
     * 流程信息
     */
    private List<FlowInfo> flowInfo;

    /**
     * 驳回字段信息
     */
    private List<RejectFieldInfo> rejectFieldInfos;

    /**
     * 子商户号信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubMchInfo {
        /**
         * 支付方式
         */
        private Integer payway;

        /**
         * 子商户号
         */
        private String subMchId;

        /**
         * 是否授权
         */
        private Boolean auth;
    }

    /**
     * 流程信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FlowInfo {
        /**
         * 状态
         */
        private Integer status;

        /**
         * 详细状态
         */
        private Integer detailStatus;

        /**
         * 详细状态描述
         */
        private String text;

        /**
         * 成功时间
         */
        private String time;
    }

    /**
     * 驳回字段信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RejectFieldInfo {
        /**
         * 驳回字段名称列表
         */
        private List<String> rejectFieldName;
        /**
         * 驳回原因
         */
        private String reason;
        /**
         * 修改建议
         */
        private String suggest;
    }
}
