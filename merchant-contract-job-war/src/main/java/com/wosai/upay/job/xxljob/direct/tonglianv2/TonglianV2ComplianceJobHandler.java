package com.wosai.upay.job.xxljob.direct.tonglianv2;

import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.job.xxljob.context.TonglianV2Context;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通联收银宝V2合规性审核补录定时任务
 *
 * <AUTHOR>
 * @date 2025/9/15
 */
@Slf4j
@Component("TonglianV2ComplianceJobHandler")
public class TonglianV2ComplianceJobHandler extends AbstractTonglianV2JobHandler {

    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private DirectStatusBiz directStatusBiz;

    /**
     * 补录成功后的固定延迟时间（分钟）
     */
    private static final int COMPLIANCE_REPAIR_SUCCESS_DELAY_MINUTES = 10;

    @Override
    public String getLockKey() {
        return "TonglianV2ComplianceJobHandler";
    }

    @Override
    protected String getJobName() {
        return "TonglianV2ComplianceJobHandler";
    }

    @Override
    protected List<PayBizApplyDO> queryPendingApplies(DirectJobParam param) {
        return queryWaitingComplianceRepairApplies(param);
    }

    @Override
    protected void processApply(TonglianV2Context context) {
        processComplianceRepair(context);
    }

    /**
     * 查询待合规性补录的申请记录
     */
    private List<PayBizApplyDO> queryWaitingComplianceRepairApplies(DirectJobParam param) {
        // 查询状态为开通中，详细状态为待补录的申请记录，按priority正序排列
        Long queryTime = param.getQueryTime();
        LocalDateTime start = LocalDateTime.now().minusSeconds(queryTime);
        return payBizApplyDAO.selectComplianceRepairApplies(DevCodeConstants.TONGLIAN_V2_DEV_CODE, start, param.getBatchSize());
    }

    /**
     * 处理合规性审核补录
     */
    private void processComplianceRepair(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        log.info("开始处理申请单合规性审核补录，applyId: {}, merchantSn: {}, detailStatus: {}",
                apply.getId(), apply.getMerchantSn(), apply.getDetailStatus());

        try {
            // 前置校验：获取商户参数和通联参数
            if (!validateAndPrepareContext(context)) {
                log.warn("前置校验失败，跳过处理，applyId: {}", apply.getId());
                delayPriority(apply);
                return;
            }

            // 调用通联接口进行合规性审核补录
            handleComplianceRepair(context);

        } catch (Exception e) {
            log.error("处理申请单合规性审核补录异常，applyId: {}", apply.getId(), e);
            // 推迟处理时间
            delayPriority(apply);
        }
    }

    /**
     * 处理合规性审核补录
     */
    private void handleComplianceRepair(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        log.info("开始进行合规性审核补录，applyId: {}, merchantSn: {}", apply.getId(), apply.getMerchantSn());

        try {
            // 构建合规性审核补录请求
            // 调用通联接口进行合规性审核补录
            Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(context.getApply().getMerchantSn());
            ContractResponse contractResponse = tongLianV2Service.repairCompliance(paramContext, apply.getHandleDetail(), context.getTongLianV2Param());

            if (contractResponse.isSuccess()) {
                handleComplianceRepairSuccess(apply);
                delayPriority(apply);
            } else {
                log.error("合规性审核补录失败，applyId: {}, response: {}", apply.getId(), contractResponse.getMessage());
                delayPriority(apply);
            }

        } catch (Exception e) {
            log.error("合规性审核补录异常，applyId: {}", apply.getId(), e);
            delayPriority(apply);
        }
    }

    /**
     * 处理合规性审核补录成功
     */
    private void handleComplianceRepairSuccess(PayBizApplyDO apply) {
        try {
            // 更新状态为合规性审核中
            payBizApplyDAO.markFlowNodeFinishedAndUpdateStatus(apply,
                    PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.COMPLIANCE_REPAIR_SUCCESS,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);

            log.info("合规性审核补录成功，转换到合规性审核中状态，applyId: {}, delayMinutes: {}",
                    apply.getId(), COMPLIANCE_REPAIR_SUCCESS_DELAY_MINUTES);

        } catch (Exception e) {
            log.error("处理合规性审核补录成功状态更新失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 推迟优先级处理时间（智能延迟策略）
     */
    private void delayPriority(PayBizApplyDO apply) {
        try {
            int delayMinutes = BASE_DELAY_MINUTES_1_3_DAYS + RANDOM.nextInt(RANDOM_DELAY_RANGE);
            LocalDateTime newPriority = LocalDateTime.now().plusMinutes(delayMinutes);
            payBizApplyDAO.updatePriority(apply, newPriority);

            log.debug("推迟处理时间，applyId: {}, delayMinutes: {}, newPriority: {}",
                    apply.getId(), delayMinutes, newPriority);
        } catch (Exception e) {
            log.error("推迟处理时间失败，applyId: {}", apply.getId(), e);
        }
    }

}
