package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 待结算人签约状态
 * 对应状态流转图中的 WAITING_SETTLEMENT_SIGN
 */
@Slf4j
@Component
public class WaitingSettlementSignState extends PayBizApplyState {

    public WaitingSettlementSignState() {
        super(PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN,
                PayBizApplyEvent.SIGN_SUCCESS,
                PayBizApplyEvent.SETTLEMENT_SIGN_FAIL,
                PayBizApplyEvent.SIGN_URL_EXPIRED);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case SIGN_SUCCESS:
                // 结算人签约完成 -> 合规补录审核中
                log.info("结算人签约完成，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING;

            case SETTLEMENT_SIGN_FAIL:
                // 结算人签约失败 -> 结算人签约失败
                log.info("结算人签约失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED;

            case SIGN_URL_EXPIRED:
                // 签约链接过期 -> 结算人签约协议失效
                log.info("结算人签约链接过期，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED;

            default:
                log.warn("待结算人签约状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入待结算人签约状态，申请单ID: {}", context.getApply().getId());
        
        // 保存签约链接（如果有）
        String signUrl = context.getSignUrl();
        if (signUrl != null) {
            context.getApply().saveSettlementSignUrl(signUrl);
            log.info("保存结算人签约链接，申请单ID: {}", context.getApply().getId());
        }
    }
}
