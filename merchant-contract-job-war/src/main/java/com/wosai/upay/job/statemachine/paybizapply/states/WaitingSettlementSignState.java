package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 待结算人签约状态
 */
@Slf4j
@Component
public class WaitingSettlementSignState extends PayBizApplyState {

    public WaitingSettlementSignState() {
        super(PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN,
                PayBizApplyEvent.SETTLEMENT_SIGN_SUCCESS,
                PayBizApplyEvent.SETTLEMENT_SIGN_FAIL,
                PayBizApplyEvent.SETTLEMENT_SIGN_EXPIRE,
                PayBizApplyEvent.ROLLBACK);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case SETTLEMENT_SIGN_SUCCESS:
                // 结算人签约成功 -> 风控审核中
                log.info("结算人签约成功，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING;

            case SETTLEMENT_SIGN_FAIL:
                // 结算人签约失败 -> 结算人签约失败
                log.info("结算人签约失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED;

            case SETTLEMENT_SIGN_EXPIRE:
                // 结算人签约过期 -> 结算人签约协议失效
                log.info("结算人签约过期，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED;

            case ROLLBACK:
                // 回退到待法人签约
                log.info("回退到待法人签约，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN;

            default:
                log.warn("待结算人签约状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        if (!validateCurrentState(context)) {
            return false;
        }

        // 对于签约过期事件，需要检查签约链接是否真的过期
        if (event == PayBizApplyEvent.SETTLEMENT_SIGN_EXPIRE) {
            return isSignUrlExpired(context);
        }

        return true;
    }

    /**
     * 检查签约链接是否过期
     */
    private boolean isSignUrlExpired(PayBizApplyContext context) {
        Long signUrlTime = context.getApply().getSignUrlTime();
        if (signUrlTime == null) {
            return false;
        }

        // 签约链接有效期7天
        long validTime = 7 * 24 * 60 * 60 * 1000L;
        long currentTime = System.currentTimeMillis();
        return currentTime - signUrlTime > validTime;
    }
}
