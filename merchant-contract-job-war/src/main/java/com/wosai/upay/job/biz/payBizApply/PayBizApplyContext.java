package com.wosai.upay.job.biz.payBizApply;

import com.wosai.upay.job.model.dto.request.PayBizApplyReqDTO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付业务开通申请上下文
 * 包装整个开通接口的上下文信息
 *
 * <AUTHOR>
@Data
@NoArgsConstructor
public class PayBizApplyContext {

    /**
     * 申请请求
     */
    private PayBizApplyReqDTO request;

    /**
     * 支付业务申请记录
     */
    private PayBizApplyDO existingApply;

    private PayBizApplyContext(PayBizApplyReqDTO request, PayBizApplyDO existingApply) {
        this.request = request;
        this.existingApply = existingApply;
    }

    public static PayBizApplyContext create(PayBizApplyReqDTO request, PayBizApplyDO existingApply) {
        return new PayBizApplyContext(request, existingApply);
    }

    /**
     * 获取商户号
     *
     * @return 商户号
     */
    public String getMerchantSn() {
        return request != null ? request.getMerchantSn() : null;
    }

    /**
     * 获取业务编码
     *
     * @return 业务编码
     */
    public String getDevCode() {
        return request != null ? request.getDevCode() : null;
    }

    /**
     * 获取表单内容
     *
     * @return 表单内容
     */
    public String getFormBody() {
        return request != null ? request.getFormBody() : null;
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public String getUserId() {
        return request != null ? request.getUserId() : null;
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public String getUserName() {
        return request != null ? request.getUserName() : null;
    }

    /**
     * 获取平台
     *
     * @return 平台
     */
    public String getPlatform() {
        return request != null ? request.getPlatform().getValue() : null;
    }

    /**
     * 判断是否存在支付业务申请记录
     *
     * @return true-存在，false-不存在
     */
    public boolean hasPayBizApply() {
        return existingApply != null;
    }

    /**
     * 获取支付业务申请记录
     *
     * @return 支付业务申请记录
     */
    public PayBizApplyDO getPayBizApply() {
        return existingApply;
    }
}