package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 启用中状态
 */
@Slf4j
@Component
public class EnablingState extends PayBizApplyState {

    public EnablingState() {
        super(PayBizApplyDetailStatusEnum.ENABLING,
                PayBizApplyEvent.ENABLE_SUCCESS,
                PayBizApplyEvent.ENABLE_FAIL);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case ENABLE_SUCCESS:
                // 启用成功 -> 开通成功
                log.info("启用成功，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.OPEN_SUCCESS;

            case ENABLE_FAIL:
                // 启用失败 -> 启用失败
                log.info("启用失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.ENABLE_FAILED;

            default:
                log.warn("启用中状态不支持事件: {}", event);
                return null;
        }
    }
}
