package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 待法人签约状态
 * 对应状态流转图中的 WAITING_LEGAL_SIGN
 */
@Slf4j
@Component
public class WaitingLegalSignState extends PayBizApplyState {

    public WaitingLegalSignState() {
        super(PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN,
                PayBizApplyEvent.SIGN_SUCCESS,
                PayBizApplyEvent.LEGAL_SIGN_FAIL,
                PayBizApplyEvent.SIGN_URL_EXPIRED);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case SIGN_SUCCESS:
                // 法人签约完成 -> 待结算人签约
                log.info("法人签约完成，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN;

            case LEGAL_SIGN_FAIL:
                // 法人签约失败 -> 法人签约失败
                log.info("法人签约失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED;

            case SIGN_URL_EXPIRED:
                // 签约链接过期 -> 法人签约协议失效
                log.info("法人签约链接过期，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED;

            default:
                log.warn("待法人签约状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入待法人签约状态，申请单ID: {}", context.getApply().getId());
        
        // 保存签约链接（如果有）
        String signUrl = context.getSignUrl();
        if (signUrl != null) {
            context.getApply().saveLegalSignUrl(signUrl);
            log.info("保存法人签约链接，申请单ID: {}", context.getApply().getId());
        }
    }

    @Override
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        if (!validateCurrentState(context)) {
            return false;
        }

        // 可以添加其他业务条件检查
        // 例如：检查是否有有效的签约链接等

        return true;
    }
}
