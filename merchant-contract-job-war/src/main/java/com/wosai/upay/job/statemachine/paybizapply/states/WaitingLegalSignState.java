package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 待法人签约状态
 */
@Slf4j
@Component
public class WaitingLegalSignState extends PayBizApplyState {

    public WaitingLegalSignState() {
        super(PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN,
                PayBizApplyEvent.LEGAL_SIGN_SUCCESS,
                PayBizApplyEvent.LEGAL_SIGN_FAIL,
                PayBizApplyEvent.LEGAL_SIGN_EXPIRE,
                PayBizApplyEvent.ROLLBACK);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case LEGAL_SIGN_SUCCESS:
                // 法人签约成功 -> 待结算人签约
                log.info("法人签约成功，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN;

            case LEGAL_SIGN_FAIL:
                // 法人签约失败 -> 法人签约失败
                log.info("法人签约失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED;

            case LEGAL_SIGN_EXPIRE:
                // 法人签约过期 -> 法人签约协议失效
                log.info("法人签约过期，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED;

            case ROLLBACK:
                // 回退到进件审核中
                log.info("回退到进件审核中，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.CONTRACT_AUDITING;

            default:
                log.warn("待法人签约状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        // 验证当前状态
        if (!validateCurrentState(context)) {
            return false;
        }

        // 对于签约过期事件，需要检查签约链接是否真的过期
        if (event == PayBizApplyEvent.LEGAL_SIGN_EXPIRE) {
            return isSignUrlExpired(context);
        }

        return true;
    }

    /**
     * 检查签约链接是否过期
     */
    private boolean isSignUrlExpired(PayBizApplyContext context) {
        Long signUrlTime = context.getApply().getSignUrlTime();
        if (signUrlTime == null) {
            return false;
        }

        // 签约链接有效期7天
        long validTime = 7 * 24 * 60 * 60 * 1000L;
        long currentTime = System.currentTimeMillis();
        return currentTime - signUrlTime > validTime;
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("等待法人签约，申请单ID: {}", context.getApply().getId());
        
        // 可以在这里执行一些动作
        // 例如：生成签约链接、发送签约通知等
    }

    @Override
    protected void onExitInternal(PayBizApplyContext context) {
        log.info("完成法人签约阶段，申请单ID: {}", context.getApply().getId());
    }
}
