package com.wosai.upay.job.statemachine.paybizapply;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.core.AbstractStateMachine;
import com.wosai.upay.job.statemachine.core.State;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 支付业务申请状态机
 * 管理支付业务申请的所有状态转换
 */
@Slf4j
@Component
public class PayBizApplyStateMachine extends AbstractStateMachine<PayBizApplyDetailStatusEnum, PayBizApplyEvent, PayBizApplyContext> {

    /**
     * 所有状态实现类（通过Spring自动注入）
     */
    @Autowired
    private List<State<PayBizApplyDetailStatusEnum, PayBizApplyEvent, PayBizApplyContext>> allStates;

    @PostConstruct
    public void init() {
        log.info("初始化支付业务申请状态机...");

        // 注册所有状态
        for (State<PayBizApplyDetailStatusEnum, PayBizApplyEvent, PayBizApplyContext> state : allStates) {
            registerState(state);
        }

        // 添加状态转换监听器
        addStateTransitionListener(new PayBizApplyStateTransitionListener());

        log.info("支付业务申请状态机初始化完成，共注册 {} 个状态", stateMap.size());
    }

    @Override
    public String getName() {
        return "PayBizApplyStateMachine";
    }

    /**
     * 状态转换监听器
     * 用于记录状态转换日志、执行业务动作等
     */
    private static class PayBizApplyStateTransitionListener implements StateTransitionListener<PayBizApplyDetailStatusEnum, PayBizApplyEvent, PayBizApplyContext> {

        @Override
        public void beforeTransition(PayBizApplyDetailStatusEnum fromState, PayBizApplyEvent event, PayBizApplyContext context) {
            log.info("状态转换开始: 申请单ID={}, 当前状态={}, 事件={}, 操作人={}",
                    context.getApply().getId(), fromState, event, context.getOperator());
        }

        @Override
        public void afterTransition(PayBizApplyDetailStatusEnum fromState, PayBizApplyDetailStatusEnum toState, PayBizApplyEvent event, PayBizApplyContext context) {
            log.info("状态转换完成: 申请单ID={}, 状态变化={}→{}, 事件={}, 操作人={}",
                    context.getApply().getId(), fromState, toState, event, context.getOperator());

            // 可以在这里执行一些通用的后置动作
            // 例如：发送状态变更通知、更新缓存等
        }
    }
}
