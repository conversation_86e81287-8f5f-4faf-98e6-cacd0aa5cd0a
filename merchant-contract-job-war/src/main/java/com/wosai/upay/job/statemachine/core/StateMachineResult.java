package com.wosai.upay.job.statemachine.core;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 状态机执行结果
 *
 * @param <S> 状态类型
 */
@Data
public class StateMachineResult<S> {

    /**
     * 是否执行成功
     */
    private boolean success;

    /**
     * 源状态
     */
    private S fromState;

    /**
     * 目标状态
     */
    private S toState;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 执行的动作列表
     */
    private List<String> executedActions = new ArrayList<>();

    /**
     * 扩展信息
     */
    private Object extension;

    /**
     * 创建成功结果
     */
    public static <S> StateMachineResult<S> success(S fromState, S toState) {
        StateMachineResult<S> result = new StateMachineResult<>();
        result.success = true;
        result.fromState = fromState;
        result.toState = toState;
        return result;
    }

    /**
     * 创建失败结果
     */
    public static <S> StateMachineResult<S> failure(S fromState, String errorMessage) {
        StateMachineResult<S> result = new StateMachineResult<>();
        result.success = false;
        result.fromState = fromState;
        result.toState = fromState; // 失败时保持原状态
        result.errorMessage = errorMessage;
        return result;
    }

    /**
     * 添加执行的动作
     */
    public void addExecutedAction(String action) {
        this.executedActions.add(action);
    }
}
