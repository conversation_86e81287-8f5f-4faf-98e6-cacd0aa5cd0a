package com.wosai.upay.job.statemachine.core;

import java.util.List;

/**
 * 状态接口
 * 定义状态的基本行为
 *
 * @param <S> 状态类型
 * @param <E> 事件类型
 * @param <C> 上下文类型
 */
public interface State<S, E, C> {

    /**
     * 获取状态标识
     *
     * @return 状态标识
     */
    S getStateId();

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    String getStateName();

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    String getStateDescription();

    /**
     * 处理事件，返回目标状态
     *
     * @param event   触发的事件
     * @param context 上下文信息
     * @return 目标状态，如果不能处理该事件则返回null
     */
    S handleEvent(E event, C context);

    /**
     * 检查是否可以处理指定事件
     *
     * @param event   事件
     * @param context 上下文信息
     * @return true-可以处理，false-不可以处理
     */
    boolean canHandle(E event, C context);

    /**
     * 获取该状态支持的事件列表
     *
     * @return 支持的事件列表
     */
    List<E> getSupportedEvents();

    /**
     * 进入状态时的动作
     *
     * @param context 上下文信息
     */
    default void onEnter(C context) {
        // 默认空实现
    }

    /**
     * 离开状态时的动作
     *
     * @param context 上下文信息
     */
    default void onExit(C context) {
        // 默认空实现
    }

    /**
     * 判断是否为终态
     *
     * @return true-终态，false-非终态
     */
    default boolean isFinalState() {
        return false;
    }
}
