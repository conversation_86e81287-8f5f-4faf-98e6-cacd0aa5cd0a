package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 合规补录审核中状态
 */
@Slf4j
@Component
public class ComplianceSupplementAuditingState extends PayBizApplyState {

    public ComplianceSupplementAuditingState() {
        super(PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING,
                PayBizApplyEvent.COMPLIANCE_AUDIT_SUCCESS,
                PayBizApplyEvent.COMPLIANCE_AUDIT_FAIL);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case COMPLIANCE_AUDIT_SUCCESS:
                // 合规审核成功 -> 待生效
                log.info("合规审核成功，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE;

            case COMPLIANCE_AUDIT_FAIL:
                // 合规审核失败 -> 合规补录驳回
                log.info("合规审核失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED;

            default:
                log.warn("合规补录审核中状态不支持事件: {}", event);
                return null;
        }
    }
}
