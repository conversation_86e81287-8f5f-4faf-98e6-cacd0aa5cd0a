package com.wosai.upay.job.statemachine.visualization;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 状态机可视化工具
 * 用于生成状态转换图和文档
 */
@Slf4j
@Component
public class StateMachineVisualizer {

    /**
     * 生成Mermaid格式的状态转换图
     */
    public String generateMermaidDiagram() {
        StringBuilder sb = new StringBuilder();
        sb.append("stateDiagram-v2\n");
        sb.append("    [*] --> CONTRACT_AUDITING : 创建申请单\n");
        
        // 进件审核阶段
        sb.append("    CONTRACT_AUDITING --> CONTRACT_AUDIT_FAILED : 审核失败\n");
        sb.append("    CONTRACT_AUDITING --> WAITING_LEGAL_SIGN : 审核成功\n");
        sb.append("    CONTRACT_AUDIT_FAILED --> CONTRACT_AUDITING : 重新提交\n");
        
        // 法人签约阶段
        sb.append("    WAITING_LEGAL_SIGN --> LEGAL_SIGN_FAILED : 签约失败\n");
        sb.append("    WAITING_LEGAL_SIGN --> LEGAL_SIGN_EXPIRED : 签约过期\n");
        sb.append("    WAITING_LEGAL_SIGN --> WAITING_SETTLEMENT_SIGN : 签约成功\n");
        sb.append("    LEGAL_SIGN_FAILED --> WAITING_LEGAL_SIGN : 重新签约\n");
        sb.append("    LEGAL_SIGN_EXPIRED --> WAITING_LEGAL_SIGN : 重新获取链接\n");
        
        // 结算人签约阶段
        sb.append("    WAITING_SETTLEMENT_SIGN --> SETTLEMENT_SIGN_FAILED : 签约失败\n");
        sb.append("    WAITING_SETTLEMENT_SIGN --> SETTLEMENT_SIGN_EXPIRED : 签约过期\n");
        sb.append("    WAITING_SETTLEMENT_SIGN --> RISK_CONTROL_AUDITING : 签约成功\n");
        sb.append("    SETTLEMENT_SIGN_FAILED --> WAITING_SETTLEMENT_SIGN : 重新签约\n");
        sb.append("    SETTLEMENT_SIGN_EXPIRED --> WAITING_SETTLEMENT_SIGN : 重新获取链接\n");
        
        // 风控审核阶段
        sb.append("    RISK_CONTROL_AUDITING --> RISK_CONTROL_REJECTED : 审核驳回\n");
        sb.append("    RISK_CONTROL_AUDITING --> WAITING_SUPPLEMENT : 需要补录\n");
        sb.append("    RISK_CONTROL_AUDITING --> COMPLIANCE_SUPPLEMENT_AUDITING : 审核成功\n");
        sb.append("    WAITING_SUPPLEMENT --> COMPLIANCE_SUPPLEMENT_AUDITING : 补录完成\n");
        
        // 合规审核阶段
        sb.append("    COMPLIANCE_SUPPLEMENT_AUDITING --> COMPLIANCE_SUPPLEMENT_REJECTED : 审核驳回\n");
        sb.append("    COMPLIANCE_SUPPLEMENT_AUDITING --> WAITING_EFFECTIVE : 审核成功\n");
        sb.append("    COMPLIANCE_SUPPLEMENT_REJECTED --> COMPLIANCE_SUPPLEMENT_AUDITING : 重新补录\n");
        
        // 启用阶段
        sb.append("    WAITING_EFFECTIVE --> ENABLING : 启用请求\n");
        sb.append("    ENABLING --> ENABLE_FAILED : 启用失败\n");
        sb.append("    ENABLING --> OPEN_SUCCESS : 启用成功\n");
        sb.append("    ENABLE_FAILED --> ENABLING : 重新启用\n");
        
        // 终态
        sb.append("    OPEN_SUCCESS --> [*]\n");
        
        return sb.toString();
    }

    /**
     * 生成状态转换表格
     */
    public String generateTransitionTable() {
        StringBuilder sb = new StringBuilder();
        sb.append("| 当前状态 | 事件 | 目标状态 | 说明 |\n");
        sb.append("|---------|------|---------|------|\n");
        
        // 定义状态转换规则
        Map<String, String[]> transitions = new HashMap<>();
        transitions.put("CONTRACT_AUDITING", new String[]{
            "CONTRACT_AUDIT_SUCCESS|WAITING_LEGAL_SIGN|进件审核成功，进入法人签约阶段",
            "CONTRACT_AUDIT_FAIL|CONTRACT_AUDIT_FAILED|进件审核失败"
        });
        
        transitions.put("WAITING_LEGAL_SIGN", new String[]{
            "LEGAL_SIGN_SUCCESS|WAITING_SETTLEMENT_SIGN|法人签约成功，进入结算人签约阶段",
            "LEGAL_SIGN_FAIL|LEGAL_SIGN_FAILED|法人签约失败",
            "LEGAL_SIGN_EXPIRE|LEGAL_SIGN_EXPIRED|法人签约链接过期",
            "ROLLBACK|CONTRACT_AUDITING|回退到进件审核阶段"
        });
        
        transitions.put("WAITING_SETTLEMENT_SIGN", new String[]{
            "SETTLEMENT_SIGN_SUCCESS|RISK_CONTROL_AUDITING|结算人签约成功，进入风控审核阶段",
            "SETTLEMENT_SIGN_FAIL|SETTLEMENT_SIGN_FAILED|结算人签约失败",
            "SETTLEMENT_SIGN_EXPIRE|SETTLEMENT_SIGN_EXPIRED|结算人签约链接过期"
        });
        
        transitions.put("RISK_CONTROL_AUDITING", new String[]{
            "RISK_AUDIT_SUCCESS|COMPLIANCE_SUPPLEMENT_AUDITING|风控审核成功，进入合规审核阶段",
            "RISK_AUDIT_FAIL|RISK_CONTROL_REJECTED|风控审核失败",
            "SUPPLEMENT_REQUIRED|WAITING_SUPPLEMENT|需要补录信息"
        });
        
        transitions.put("COMPLIANCE_SUPPLEMENT_AUDITING", new String[]{
            "COMPLIANCE_AUDIT_SUCCESS|WAITING_EFFECTIVE|合规审核成功，等待生效",
            "COMPLIANCE_AUDIT_FAIL|COMPLIANCE_SUPPLEMENT_REJECTED|合规审核失败"
        });
        
        transitions.put("WAITING_EFFECTIVE", new String[]{
            "ENABLE_REQUEST|ENABLING|用户请求启用"
        });
        
        transitions.put("ENABLING", new String[]{
            "ENABLE_SUCCESS|OPEN_SUCCESS|启用成功，开通完成",
            "ENABLE_FAIL|ENABLE_FAILED|启用失败"
        });
        
        // 生成表格内容
        for (Map.Entry<String, String[]> entry : transitions.entrySet()) {
            String currentState = entry.getKey();
            for (String transition : entry.getValue()) {
                String[] parts = transition.split("\\|");
                if (parts.length == 3) {
                    sb.append(String.format("| %s | %s | %s | %s |\n", 
                            currentState, parts[0], parts[1], parts[2]));
                }
            }
        }
        
        return sb.toString();
    }

    /**
     * 生成状态机使用文档
     */
    public String generateUsageDocument() {
        StringBuilder sb = new StringBuilder();
        sb.append("# PayBizApply状态机使用文档\n\n");
        
        sb.append("## 概述\n");
        sb.append("PayBizApply状态机管理支付业务申请的整个生命周期，包括进件审核、签约、风控审核、合规审核和启用等阶段。\n\n");
        
        sb.append("## 状态说明\n");
        for (PayBizApplyDetailStatusEnum status : PayBizApplyDetailStatusEnum.values()) {
            sb.append(String.format("- **%s**: %s\n", status.name(), status.getDescription()));
        }
        sb.append("\n");
        
        sb.append("## 事件说明\n");
        for (PayBizApplyEvent event : PayBizApplyEvent.values()) {
            sb.append(String.format("- **%s**: %s\n", event.name(), event.getDescription()));
        }
        sb.append("\n");
        
        sb.append("## 使用示例\n");
        sb.append("```java\n");
        sb.append("// 进件审核成功\n");
        sb.append("boolean success = stateMachineHelper.contractAuditSuccess(apply, \"SYSTEM\", \"SYSTEM\", \"JOB\");\n\n");
        sb.append("// 法人签约失败\n");
        sb.append("boolean success = stateMachineHelper.legalSignFail(apply, \"USER\", \"123\", \"WEB\");\n\n");
        sb.append("// 检查是否可以执行某个操作\n");
        sb.append("boolean canEnable = stateMachineHelper.canTransition(apply, PayBizApplyEvent.ENABLE_REQUEST);\n");
        sb.append("```\n\n");
        
        sb.append("## 状态转换图\n");
        sb.append("```mermaid\n");
        sb.append(generateMermaidDiagram());
        sb.append("```\n\n");
        
        sb.append("## 状态转换表\n");
        sb.append(generateTransitionTable());
        
        return sb.toString();
    }
}
