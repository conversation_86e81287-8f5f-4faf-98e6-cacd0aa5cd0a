package com.wosai.upay.job.statemachine.util;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.statemachine.core.StateMachineResult;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.service.PayBizApplyStateMachineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 状态机辅助工具类
 * 提供便捷的状态机操作方法
 */
@Slf4j
@Component
public class StateMachineHelper {

    @Autowired
    private PayBizApplyStateMachineService stateMachineService;

    /**
     * 安全地执行状态转换（带降级处理）
     * 
     * @param apply 申请单
     * @param event 事件
     * @param operator 操作人
     * @param operatorId 操作人ID
     * @param platform 平台
     * @param reason 原因
     * @param fallbackAction 降级处理动作
     * @return 是否成功
     */
    public boolean safeFireEvent(PayBizApplyDO apply, PayBizApplyEvent event, 
                               String operator, String operatorId, String platform, String reason,
                               Runnable fallbackAction) {
        try {
            StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachineService.fireEvent(
                    apply, event, operator, operatorId, platform, reason);
            
            if (result.isSuccess()) {
                log.info("状态机转换成功: 申请单ID={}, 事件={}, 状态={}→{}", 
                        apply.getId(), event, result.getFromState(), result.getToState());
                return true;
            } else {
                log.warn("状态机转换失败: 申请单ID={}, 事件={}, 错误={}", 
                        apply.getId(), event, result.getErrorMessage());
                
                // 执行降级处理
                if (fallbackAction != null) {
                    log.info("执行降级处理: 申请单ID={}, 事件={}", apply.getId(), event);
                    fallbackAction.run();
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
            log.error("状态机转换异常: 申请单ID={}, 事件={}", apply.getId(), event, e);
            
            // 执行降级处理
            if (fallbackAction != null) {
                log.info("执行降级处理: 申请单ID={}, 事件={}", apply.getId(), event);
                fallbackAction.run();
                return true;
            }
            return false;
        }
    }

    /**
     * 检查是否可以执行状态转换
     */
    public boolean canTransition(PayBizApplyDO apply, PayBizApplyEvent event) {
        try {
            return stateMachineService.canTransition(apply, event);
        } catch (Exception e) {
            log.error("检查状态转换能力异常: 申请单ID={}, 事件={}", apply.getId(), event, e);
            return false;
        }
    }

    /**
     * 进件审核成功
     */
    public boolean contractAuditSuccess(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.CONTRACT_AUDIT_SUCCESS, 
                operator, operatorId, platform, "进件审核成功", null);
    }

    /**
     * 进件审核失败
     */
    public boolean contractAuditFail(PayBizApplyDO apply, String operator, String operatorId, String platform, String reason) {
        return safeFireEvent(apply, PayBizApplyEvent.CONTRACT_AUDIT_FAIL, 
                operator, operatorId, platform, "进件审核失败: " + reason, 
                () -> apply.updateStatusToContractFailed(reason));
    }

    /**
     * 法人签约成功
     */
    public boolean legalSignSuccess(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.LEGAL_SIGN_SUCCESS, 
                operator, operatorId, platform, "法人签约成功", null);
    }

    /**
     * 法人签约失败
     */
    public boolean legalSignFail(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.LEGAL_SIGN_FAIL, 
                operator, operatorId, platform, "法人签约失败", 
                () -> apply.updateToSignFailed());
    }

    /**
     * 法人签约过期
     */
    public boolean legalSignExpire(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.LEGAL_SIGN_EXPIRE, 
                operator, operatorId, platform, "法人签约链接过期", 
                () -> apply.updateStatusToExpired());
    }

    /**
     * 结算人签约成功
     */
    public boolean settlementSignSuccess(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.SETTLEMENT_SIGN_SUCCESS, 
                operator, operatorId, platform, "结算人签约成功", null);
    }

    /**
     * 结算人签约失败
     */
    public boolean settlementSignFail(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.SETTLEMENT_SIGN_FAIL, 
                operator, operatorId, platform, "结算人签约失败", 
                () -> apply.updateToSignFailed());
    }

    /**
     * 结算人签约过期
     */
    public boolean settlementSignExpire(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.SETTLEMENT_SIGN_EXPIRE, 
                operator, operatorId, platform, "结算人签约链接过期", 
                () -> apply.updateStatusToExpired());
    }

    /**
     * 风控审核成功
     */
    public boolean riskAuditSuccess(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.RISK_AUDIT_SUCCESS, 
                operator, operatorId, platform, "风控审核成功", null);
    }

    /**
     * 风控审核失败
     */
    public boolean riskAuditFail(PayBizApplyDO apply, String operator, String operatorId, String platform, String reason) {
        return safeFireEvent(apply, PayBizApplyEvent.RISK_AUDIT_FAIL, 
                operator, operatorId, platform, "风控审核失败: " + reason, null);
    }

    /**
     * 合规审核成功
     */
    public boolean complianceAuditSuccess(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.COMPLIANCE_AUDIT_SUCCESS, 
                operator, operatorId, platform, "合规审核成功", null);
    }

    /**
     * 合规审核失败
     */
    public boolean complianceAuditFail(PayBizApplyDO apply, String operator, String operatorId, String platform, String reason, String handleDetail) {
        return safeFireEvent(apply, PayBizApplyEvent.COMPLIANCE_AUDIT_FAIL, 
                operator, operatorId, platform, "合规审核失败: " + reason, 
                () -> apply.updateToComplianceFailed(reason, handleDetail));
    }

    /**
     * 启用请求
     */
    public boolean enableRequest(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.ENABLE_REQUEST, 
                operator, operatorId, platform, "用户请求启用", null);
    }

    /**
     * 启用成功
     */
    public boolean enableSuccess(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        return safeFireEvent(apply, PayBizApplyEvent.ENABLE_SUCCESS, 
                operator, operatorId, platform, "启用成功", 
                () -> apply.markAsSuccess());
    }

    /**
     * 启用失败
     */
    public boolean enableFail(PayBizApplyDO apply, String operator, String operatorId, String platform, String reason) {
        return safeFireEvent(apply, PayBizApplyEvent.ENABLE_FAIL, 
                operator, operatorId, platform, "启用失败: " + reason, 
                () -> apply.updateStatusToEnabledFailed(reason));
    }

    /**
     * 状态回退
     */
    public boolean rollback(PayBizApplyDO apply, String operator, String operatorId, String platform, String reason) {
        return safeFireEvent(apply, PayBizApplyEvent.ROLLBACK, 
                operator, operatorId, platform, "状态回退: " + reason, 
                () -> apply.rollbackStatus());
    }
}
