package com.wosai.upay.job.statemachine.core;

/**
 * 状态机接口
 * 定义状态机的基本操作
 *
 * @param <S> 状态类型
 * @param <E> 事件类型
 * @param <C> 上下文类型
 */
public interface StateMachine<S, E, C> {

    /**
     * 触发事件，执行状态转换
     *
     * @param currentState 当前状态
     * @param event        触发的事件
     * @param context      上下文信息
     * @return 状态转换结果
     */
    StateMachineResult<S> fireEvent(S currentState, E event, C context);

    /**
     * 检查是否可以执行指定的状态转换
     *
     * @param fromState 源状态
     * @param event     触发的事件
     * @param context   上下文信息
     * @return true-可以转换，false-不可以转换
     */
    boolean canTransition(S fromState, E event, C context);

    /**
     * 获取指定状态可以转换到的目标状态列表
     *
     * @param fromState 源状态
     * @param context   上下文信息
     * @return 可转换的目标状态列表
     */
    java.util.List<S> getAvailableTransitions(S fromState, C context);

    /**
     * 获取状态机名称
     *
     * @return 状态机名称
     */
    String getName();
}
