package com.wosai.upay.job.biz.payBizApply;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.notification.req.ContractSupplTransferReq;
import com.wosai.notification.res.ContractSupplTransferRes;
import com.wosai.notification.service.ContractSupplService;
import com.wosai.sp.common.data.riskAudit.RejectAuditTemplateDTO;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.dto.request.PayBizApplyStatusReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizEnabledSetReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignResendReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusDetailRspDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusRspDTO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowInfoBO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowNodeBO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.AuthStatusEnum;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.service.PayBizApplyStateMachineService;
import com.wosai.upay.job.statemachine.core.StateMachineResult;
import com.wosai.upay.job.util.DateUtil;
import com.wosai.upay.merchant.audit.api.pojo.req.ContractSupplAuditSubmitReqDTO;
import com.wosai.upay.merchant.audit.api.pojo.resp.ContractSupplAuditResultResDTO;
import com.wosai.upay.merchant.audit.api.service.ContractSupplAuditService;
import com.wosai.upay.merchant.contract.enume.tonglianv2.TonglianV2ComplianceRejectEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.tlV2.request.ElectSignResendRequest;
import com.wosai.upay.merchant.contract.model.tlV2.request.ElectUrlQueryRequest;
import com.wosai.upay.merchant.contract.model.tlV2.response.ElectUrlQueryResponse;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wosai.upay.job.constant.ContractRuleConstants.CHANGE_TO_TONGLIANV2_RULE_GROUP;

/**
 * 通联收银宝V2支付业务开通申请处理服务
 *
 * <AUTHOR>
@Slf4j
@Component
public class TonglianV2PayBizApplyHandleService extends AbstractPayBizApplyHandleService {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private AcquirerService acquirerService;
    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private CommonEventHandler commonEventHandler;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private TongLianV2Service tongLianV2Service;
    @Autowired
    private ContractSupplAuditService contractSupplAuditService;
    @Autowired
    private ContractSupplService contractSupplService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private AcquirerChangeDao changeDao;
    @Autowired
    private DirectStatusBiz directStatusBiz;
    @Autowired
    private PayBizApplyStateMachineService stateMachineService;

    // 常量定义
    private static final String BUSINESS_NAME = "通联收银宝V2";
    private static final String MERCHANT_CONFIG_KEY = "merchant_config";
    private static final String TONGLIAN_V2_FEE_RATE_KEY = "tonglianV2_fee_rate";
    private static final String TASK_TYPE_NET_IN = "新增商户入网";
    private static final List<Integer> ALI_WX_PAYWAY = Arrays.asList(PaywayEnum.WEIXIN.getValue(), PaywayEnum.ALIPAY.getValue());
    private static final long SIGN_URL_VALID_TIME = 7 * 24 * 60 * 60 * 1000L;

    @Override
    public String getDevCode() {
        return DevCodeConstants.TONGLIAN_V2_DEV_CODE;
    }

    @Override
    public String getBusinessName() {
        return BUSINESS_NAME;
    }

    @Override
    public PayBizApplyFlowInfoBO initFlowInfo(PayBizApplyContext context) {
        // 判断是否需要待结算人签约节点
        boolean needSettlementSign = needSettlementSignNode(context.getMerchantSn());

        if (needSettlementSign) {
            return PayBizApplyFlowInfoBO.createFlowWithSettlementSign();
        } else {
            return PayBizApplyFlowInfoBO.createFlowWithoutSettlementSign();
        }
    }

    @Override
    public void specialCheck(PayBizApplyContext context) {
    }

    /**
     * 创建新的申请单
     */
    @Override
    public CuaCommonResultDTO createNewApply(PayBizApplyContext context) {
        try {
            log.info("创建新的{}申请单，merchantSn: {}, devCode: {}", getBusinessName(), context.getMerchantSn(), context.getDevCode());

            // 初始化流程信息
            PayBizApplyFlowInfoBO flowInfo = initFlowInfo(context);
            // 4. 生成任务，并插入任务信息
            final Long taskId = createTask(context);
            // 创建新的申请单
            PayBizApplyDO newApply = payBizApplyDAO.createNewApply(context.getMerchantSn(), context.getDevCode(), context.getFormBody(), taskId, flowInfo);
            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(newApply.getId()), PayBizApplyOperationEnum.CREATE_APPLY, context.getUserName(), context.getUserId(), context.getPlatform(),
                    PayBizApplyLogDetailBO.create(newApply.getStatus(), newApply.getDetailStatus()));
            directStatusBiz.createOrUpdateDirectStatus(newApply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);

            log.info("成功创建{}申请单，申请单ID: {}, merchantSn: {}, devCode: {}",
                    getBusinessName(), newApply.getId(), context.getMerchantSn(), context.getDevCode());

            return CuaCommonResultDTO.success("申请提交成功");

        } catch (Exception e) {
            log.error("创建{}申请单失败，merchantSn: {}, devCode: {}", getBusinessName(), context.getMerchantSn(), context.getDevCode(), e);
            throw new ContractBizException("创建申请单失败：" + e.getMessage());
        }
    }

    /**
     * 处理已存在的申请单
     */
    @Override
    public CuaCommonResultDTO handleExistingApply(PayBizApplyContext context) {
        PayBizApplyDO existingApply = context.getExistingApply();
        Integer status = existingApply.getStatus();
        Integer detailStatus = existingApply.getDetailStatus();

        log.info("处理已存在的{}申请单，申请单ID: {}, status: {}, detailStatus: {}",
                getBusinessName(), existingApply.getId(), status, detailStatus);

        try {
            // 如果进件失败->提交并保存进件任务
            if (PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED.getCode().equals(detailStatus)) {
                Long taskId = createTask(context);
                boolean rollbackSuccess = payBizApplyDAO.rollbackStatusAndUpdateForm(existingApply, context.getFormBody(), taskId);
                if (!rollbackSuccess) {
                    throw new ContractBizException("状态回退失败，当前状态不支持回退");
                }
            }
            // 如果签约失败->待签约
            else if (PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED.getCode().equals(detailStatus) || PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED.getCode().equals(detailStatus)) {
                boolean rollbackSuccess = payBizApplyDAO.rollbackStatusAndUpdateForm(existingApply, context.getFormBody(), null);
                if (!rollbackSuccess) {
                    throw new ContractBizException("状态回退失败，当前状态不支持回退");
                }
            }
            // 如果合规性审核失败->提交风控审核 & 变为风控审核中 如果风控审核驳回->提交风控审核 & 变为风控审核中
            else if (PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED.getCode().equals(detailStatus) || PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED.getCode().equals(detailStatus)) {
                Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(context.getMerchantSn(), ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());
                if (!acquirerParams.isPresent()) {
                    throw new ContractBizException("未找到通联收银宝相关参数");
                }
                ContractSupplAuditSubmitReqDTO contractSupplAuditSubmitReqDTO = ContractSupplAuditSubmitReqDTO.builder()
                        .merchantSn(existingApply.getMerchantSn())
                        .contractType(AcquirerTypeEnum.TONG_LIAN_V2.getValue())
                        .providerMerchantSn(acquirerParams.get().getPayMerchantId())
                        .submitterPlatform(context.getPlatform())
                        .submitter(context.getUserName())
                        .submitterId(context.getUserId())
                        .build();
                contractSupplAuditService.submitContractSupplAudit(contractSupplAuditSubmitReqDTO);
                boolean rollbackSuccess = payBizApplyDAO.rollbackStatusAndUpdateForm(existingApply, context.getFormBody(), null);
                if (!rollbackSuccess) {
                    throw new ContractBizException("状态回退失败，当前状态不支持回退");
                }
            }
            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(existingApply.getId()), PayBizApplyOperationEnum.RESUBMIT_APPLY, context.getUserName(), context.getUserId(), context.getPlatform(),
                    PayBizApplyLogDetailBO.create(existingApply.getStatus(), existingApply.getDetailStatus()));
            directStatusBiz.createOrUpdateDirectStatus(existingApply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);

            log.info("成功重新提交{}申请单，申请单ID: {}", getBusinessName(), existingApply.getId());
            return CuaCommonResultDTO.success("申请重新提交成功");

        } catch (Exception e) {
            log.error("重新提交{}申请单失败，申请单ID: {}", getBusinessName(), existingApply.getId(), e);
            throw new ContractBizException("重新提交失败：" + e.getMessage());
        }
    }

    /**
     * 构建状态详情响应
     */
    @Override
    public PayBizApplyStatusDetailRspDTO doBuildStatusDetailResponse(PayBizApplyDO apply, PayBizApplyStatusReqDTO request) {
        PayBizApplyStatusDetailRspDTO response = new PayBizApplyStatusDetailRspDTO();

        // 基本状态信息
        PayBizApplyStatusEnum statusEnum = apply.getStatusEnum();
        PayBizApplyDetailStatusEnum detailStatusEnum = apply.getDetailStatusEnum();
        response.setStatus(statusEnum.getCode());
        response.setDetailStatus(detailStatusEnum.getCode());
        response.setStatusDesc(statusEnum.getDesc());
        response.setDetailStatusDesc(detailStatusEnum.getDesc());
        response.setDisplayText(detailStatusEnum.getDisplayText());

        // 失败原因
        if (apply.isFailed() || apply.isEnabledFailed()) {
            ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(request.getPlatform().getValue(), apply.getResult(), AcquirerTypeEnum.TONG_LIAN_V2.getValue());
            response.setDescription(errorInfo.getMsg());
            response.setRejectFieldInfos(transRejectFieldInfo(apply));
        } else {
            response.setDescription(detailStatusEnum.getDescription());
        }
        response.setSignUrl(apply.getSignUrl());
        // 子商户号信息
        if (apply.isInEnablingStage() || apply.isSuccess()) {
            List<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getParamsBySnAndPayWayListAndProvider(apply.getMerchantSn(), ALI_WX_PAYWAY, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
            List<PayBizApplyStatusDetailRspDTO.SubMchInfo> subMchInfoList = new ArrayList<>();
            for (MerchantProviderParamsDO param : params) {
                PayBizApplyStatusDetailRspDTO.SubMchInfo subMchInfo = new PayBizApplyStatusDetailRspDTO.SubMchInfo();
                subMchInfo.setPayway(param.getPayway());
                subMchInfo.setSubMchId(param.getPayMerchantId());
                subMchInfo.setAuth(param.getAuthStatus().equals(AuthStatusEnum.YES.getValue()));
                subMchInfoList.add(subMchInfo);
            }
            response.setSubMchInfo(subMchInfoList);
        }
        // 流程信息
        response.setFlowInfo(buildFlowInfo(apply));
        return response;
    }

    @Override
    public PayBizApplyStatusRspDTO doBuildPayBizApplyStatusInfo(PayBizApplyDO apply, PayBizApplyStatusReqDTO request) {
        PayBizApplyStatusRspDTO response = new PayBizApplyStatusRspDTO();
        PayBizApplyDetailStatusEnum detailStatusEnum = apply.getDetailStatusEnum();
        response.setStatus(apply.getStatus());
        response.setDetailStatus(apply.getDetailStatus());
        response.setStatusDesc(apply.getStatusEnum().getDesc());
        response.setDetailStatusDesc(detailStatusEnum.getDesc());
        if (apply.isFailed() || apply.isEnabledFailed()) {
            ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(request.getPlatform().getValue(), apply.getResult(), AcquirerTypeEnum.TONG_LIAN_V2.getValue());
            response.setDescription(errorInfo.getMsg());
        } else {
            response.setDescription(detailStatusEnum.getDescription());
        }
        response.setDisplayText(detailStatusEnum.getDisplayText());
        return response;
    }

    @Override
    public void doSetPayBizEnabled(PayBizEnabledSetReqDTO request, PayBizApplyDO apply) {
        // 1.检查是否可以执行启用操作
        if (!stateMachineService.canTransition(apply, PayBizApplyEvent.ENABLE_REQUEST)) {
            throw new ContractBizException("当前状态不支持设置支付业务是否启用");
        }

        // 2.如果是启用，则去切换收单机构
        if (request.getEnabled()) {
            // 先执行状态转换到启用中
            StateMachineResult<PayBizApplyDetailStatusEnum> enableResult = stateMachineService.fireEvent(
                    apply,
                    PayBizApplyEvent.ENABLE_REQUEST,
                    request.getUserName(),
                    request.getUserId(),
                    request.getPlatform().getValue(),
                    "用户手动启用支付业务"
            );

            if (!enableResult.isSuccess()) {
                throw new ContractBizException("状态转换失败: " + enableResult.getErrorMessage());
            }

            // 执行收单机构切换
            Tuple2<Boolean, String> applyResult = doApplyChangeAcquirer(apply);
            if (!applyResult.get_1()) {
                // 切换失败，转换到启用失败状态
                stateMachineService.fireEvent(
                        apply,
                        PayBizApplyEvent.ENABLE_FAIL,
                        request.getUserName(),
                        request.getUserId(),
                        request.getPlatform().getValue(),
                        "收单机构切换失败: " + applyResult.get_2()
                );
                directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);
            } else {
                // 切换成功，更新申请单的切换申请ID
                apply.updateStatusToEnabling(applyResult.get_2());
                directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);
            }
        } else {
            // 3.如果是不启用，则直接设置为成功
            StateMachineResult<PayBizApplyDetailStatusEnum> successResult = stateMachineService.fireEvent(
                    apply,
                    PayBizApplyEvent.ENABLE_SUCCESS,
                    request.getUserName(),
                    request.getUserId(),
                    request.getPlatform().getValue(),
                    "用户选择不启用，直接完成"
            );

            if (successResult.isSuccess()) {
                directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_SUCCESS, null);
            }
        }
    }

    @Override
    public void doReSendSignUrl(PayBizSignResendReqDTO request, PayBizApplyDO apply) {
        // 检查当前状态是否允许重新发送签约链接，只有法人签约失效/结算人签约失效才可以重新发送签约链接
        if (!apply.isSignUrlExpired()) {
            throw new ContractBizException("当前状态不支持重新发送签约链接");
        }
        Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(request.getMerchantSn(), ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());
        if (!acquirerParams.isPresent()) {
            throw new ContractBizException("收单机构参数不存咋");
        }
        MerchantProviderParamsDO merchantProviderParam = acquirerParams.get();
        TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParams(merchantProviderParam.getProvider() + "", merchantProviderParam.getPayway(), merchantProviderParam.getChannelNo(), TongLianV2Param.class);
        ElectSignResendRequest electSignResendRequest = new ElectSignResendRequest();
        electSignResendRequest.setAcquirerMerchantId(merchantProviderParam.getPayMerchantId());
        electSignResendRequest.setType(PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode().equals(apply.getDetailStatus()) ? 1 : 2);
        ContractResponse resendResponse = tongLianV2Service.resendElectSignUrl(electSignResendRequest, tongLianV2Param);
        if (!resendResponse.isSuccess()) {
            throw new ContractBizException("重新发送签约链接失败：" + resendResponse.getMessage());
        }
        ElectUrlQueryRequest electUrlQueryRequest = new ElectUrlQueryRequest();
        electUrlQueryRequest.setAcquirerMerchantId(merchantProviderParam.getPayMerchantId());
        electUrlQueryRequest.setType(PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode().equals(apply.getDetailStatus()) ? 1 : 2);
        ContractResponse contractResponse = tongLianV2Service.queryElectUrlV2(electUrlQueryRequest, tongLianV2Param);
        if (!contractResponse.isSuccess()) {
            throw new ContractBizException("查询签约链接失败：" + contractResponse.getMessage());
        }
        ElectUrlQueryResponse queryResponse = JSON.parseObject(JSON.toJSONString(contractResponse.getResponseParam()), ElectUrlQueryResponse.class);
        String url = queryResponse.getSybsignurl();
        payBizApplyDAO.updateSignUrl(apply, url);
        payBizApplyLogDAO.logOperationWithJsonDetail(
                String.valueOf(apply.getId()), PayBizApplyOperationEnum.RESIGN, request.getUserName(), request.getUserId(), request.getPlatform().getValue(),
                PayBizApplyLogDetailBO.create(apply.getStatus(), apply.getDetailStatus()));
        directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);
    }

    @Override
    public CuaCommonResultDTO doValidateSignUrl(PayBizApplyDO apply) {
        try {
            // 1. 判断当前申请单的状态，如果当前申请单不在签约中/签约链接失效状态则返回false和对应的文案
            if (!apply.isInSigningOrExpiredStatus()) {
                String statusDesc = getStatusDescription(apply.getStatus(), apply.getDetailStatus());
                String message = String.format("当前申请单状态为：%s", statusDesc);
                log.info("校验签约链接过期结果：状态不符合，申请单ID: {}, 状态: {}", apply.getId(), statusDesc);
                return CuaCommonResultDTO.fail(message);
            }

            // 2. 判断对应签约链接对应的时间，PayBizApplyDO.getSignUrlTime返回值>当前时刻，则认为有效
            Long signUrlTime = apply.getSignUrlTime();
            if (signUrlTime == null) {
                String message = "签约链接时间为空，无法校验过期状态";
                log.warn("校验签约链接过期失败：签约链接时间为空，申请单ID: {}", apply.getId());
                return CuaCommonResultDTO.fail(message);
            }
            long currentTime = System.currentTimeMillis();
            boolean isValid = currentTime - signUrlTime < SIGN_URL_VALID_TIME;
            if (isValid) {
                return CuaCommonResultDTO.success("签约链接有效");
            } else {
                // 3. 如果无效则需要修改申请单状态为对应的失效状态，记录日志，通过DirectStatusBiz发送消息
                log.info("校验签约链接过期结果：已过期，申请单ID: {}, 签约链接时间: {}, 当前时间: {}",
                        apply.getId(), signUrlTime, currentTime);
                // 更新申请单状态为失效状态
                payBizApplyDAO.updateToExpired(apply);
                // 记录操作日志
                payBizApplyLogDAO.logOperationWithJsonDetail(
                        String.valueOf(apply.getId()),
                        PayBizApplyOperationEnum.SIGN_URL_EXPIRED,
                        "SYSTEM",
                        "SYSTEM",
                        "SYSTEM",
                        PayBizApplyLogDetailBO.create(apply.getStatus(), apply.getDetailStatus()));

                // 通过DirectStatusBiz发送消息
                directStatusBiz.createOrUpdateDirectStatus(
                        apply.getMerchantSn(),
                        DevCodeConstants.TONGLIAN_V2_DEV_CODE,
                        DirectStatus.STATUS_PROCESS,
                        null);
                return CuaCommonResultDTO.fail("签约链接已过期");
            }

        } catch (Exception e) {
            log.error("校验签约链接过期异常，申请单ID: {}", apply.getId(), e);
            return CuaCommonResultDTO.fail("校验签约链接过期异常：" + e.getMessage());
        }
    }

    /**
     * 构建流程信息
     */
    private List<PayBizApplyStatusDetailRspDTO.FlowInfo> buildFlowInfo(PayBizApplyDO apply) {
        List<PayBizApplyStatusDetailRspDTO.FlowInfo> flowInfoList = new ArrayList<>();

        try {
            PayBizApplyFlowInfoBO flowInfoBO = apply.getFlowInfoBO();
            if (flowInfoBO != null && flowInfoBO.getNodes() != null) {
                for (PayBizApplyFlowNodeBO node : flowInfoBO.getNodes()) {
                    PayBizApplyStatusDetailRspDTO.FlowInfo flowInfo = new PayBizApplyStatusDetailRspDTO.FlowInfo();
                    flowInfo.setStatus(node.getStatus());
                    flowInfo.setDetailStatus(node.getDetailStatus());
                    flowInfo.setText(node.getText());

                    // 格式化时间
                    if (node.getTime() != null) {
                        flowInfo.setTime(node.getTime().format(DateUtil.DATE_TIME_FORMATTER));
                    } else {
                        flowInfo.setTime("");
                    }

                    flowInfoList.add(flowInfo);
                }
            }
        } catch (Exception e) {
            log.error("构建流程信息失败，申请单ID: {}", apply.getId(), e);
        }

        return flowInfoList;
    }

    private boolean otherPersonSettlement(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(WosaiMapUtils.getString(merchant, DaoConstants.ID));
        if (WosaiMapUtils.getIntValue(merchantBankAccount, MerchantBankAccount.TYPE) == 1) {
            return !WosaiStringUtils.equals(merchantBusinessLicenseInfo.getLegal_person_id_number(), WosaiMapUtils.getString(merchantBankAccount, MerchantBankAccount.IDENTITY));
        }
        return false;
    }

    /**
     * 判断是否需要待结算人签约节点
     * 通联收银宝V2的业务规则：法人和结算人不是同一人时需要待结算人签约
     *
     * @param merchantSn 商户号
     * @return true-需要，false-不需要
     */
    private boolean needSettlementSignNode(String merchantSn) {
        return otherPersonSettlement(merchantSn);
    }

    /**
     * 创建任务
     *
     * @param context 申请上下文
     * @return 任务ID
     */
    private Long createTask(PayBizApplyContext context) {
        log.info("创建通联收银宝V2任务，merchantSn: {}", context.getMerchantSn());

        // 生成任务，并插入任务信息
        ContractEvent contractEvent = createContractEvent(context.getMerchantSn());
        final Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(context.getMerchantSn(), contractEvent);

        // 解析表单数据并放入费率配置
        Map formBody = JSON.parseObject(context.getFormBody(), Map.class);
        List config = (List) formBody.get(MERCHANT_CONFIG_KEY);
        paramContext.put(TONGLIAN_V2_FEE_RATE_KEY, config);

        // 处理事件插入
        commonEventHandler.handleInsert(contractEvent, paramContext);

        // 获取任务ID，用于关联任务和申请记录
        Long taskId = getTaskId(contractEvent.getRule_group_id(), context.getMerchantSn());
        log.info("成功创建通联收银宝V2任务，merchantSn: {}, taskId: {}", context.getMerchantSn(), taskId);

        return taskId;
    }

    /**
     * 创建合约事件
     *
     * @param merchantSn 商户号
     * @return 合约事件
     */
    protected ContractEvent createContractEvent(String merchantSn) {
        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setMerchant_sn(merchantSn);
        contractEvent.setRule_group_id(CHANGE_TO_TONGLIANV2_RULE_GROUP);
        contractEvent.setEvent_type(ContractEvent.OPT_TYPE_NET_IN);
        return contractEvent;
    }

    /**
     * 获取任务ID
     *
     * @param groupId    规则组ID
     * @param merchantSn 商户号
     * @return 任务ID
     */
    public Long getTaskId(String groupId, String merchantSn) {
        final List<ContractTask> contractTaskList = contractTaskMapper.getContractsBySnAndType(merchantSn, TASK_TYPE_NET_IN);
        final List<ContractTask> contractTasks = Optional.ofNullable(contractTaskList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(task -> Objects.equals(groupId, task.getRule_group_id()) &&
                        (TaskStatus.PENDING.getVal().equals(task.getStatus()) || TaskStatus.PROGRESSING.getVal().equals(task.getStatus())))
                .sorted(Comparator.comparing(ContractTask::getCreate_at,
                        Comparator.nullsLast(Date::compareTo)).reversed())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(contractTasks)) {
            throw new CommonPubBizException(String.format("商户号%s还没有生成进件任务", merchantSn));
        }
        return contractTasks.get(0).getId();
    }

    private Tuple2<Boolean, String> doApplyChangeAcquirer(PayBizApplyDO apply) {
        try {
            acquirerService.applyChangeAcquirer(apply.getMerchantSn(), AcquirerTypeEnum.TONG_LIAN_V2.getValue(), true);
            McAcquirerChange latestUnFinishedApply = changeDao.getLatestUnFinishedApply(apply.getMerchantSn());
            return new Tuple2<>(true, latestUnFinishedApply.getApply_id());
        } catch (Exception e) {
            log.error("申请切换收单机构失败，merchantSn: {}, error: {}", apply.getMerchantSn(), e.getMessage(), e);
            return new Tuple2<>(false, "切换收单机构失败" + e.getMessage());
        }
    }

    private List<PayBizApplyStatusDetailRspDTO.RejectFieldInfo> transRejectFieldInfo(PayBizApplyDO apply) {
        if (apply.isDetailComplianceReject()) {
            // 字段转换
            Map<String, String> handleDetailMap = JSON.parseObject(apply.getHandleDetail(), new TypeReference<Map<String, String>>() {
            });
            Map<String, String> tonglianV2ComplianceFieldMapping = applicationApolloConfig.getTonglianV2ComplianceFieldMapping();
            List<ContractSupplTransferReq.RejectInfo> rejectInfos = new ArrayList<>();
            for (Map.Entry<String, String> stringStringEntry : handleDetailMap.entrySet()) {
                if (!"1".equals(stringStringEntry.getValue())) {
                    TonglianV2ComplianceRejectEnum rejectEnum = TonglianV2ComplianceRejectEnum.findByFieldIdAndValue(stringStringEntry.getKey(), stringStringEntry.getValue());
                    ContractSupplTransferReq.RejectInfo rejectInfo = new ContractSupplTransferReq.RejectInfo();
                    String rejectSqbField = tonglianV2ComplianceFieldMapping.get(stringStringEntry.getKey());
                    if (WosaiStringUtils.isNotEmpty(rejectSqbField)) {
                        rejectInfo.setFieldKey(rejectEnum.getFieldId());
                        rejectInfo.setReason(rejectEnum.getDescription());
                        rejectInfos.add(rejectInfo);
                    }
                }
            }
            Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(apply.getMerchantSn(), ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());
            if (!acquirerParams.isPresent()) {
                log.error("未找到通联收银宝相关参数 {}", apply.getMerchantSn());
                return Collections.emptyList();
            }
            ContractSupplTransferReq contractSupplTransferReq = new ContractSupplTransferReq();
            contractSupplTransferReq.setMerchantSn(apply.getMerchantSn());
            contractSupplTransferReq.setContractType(AcquirerTypeEnum.TONG_LIAN_V2.getValue());
            contractSupplTransferReq.setProviderMerchantSn(acquirerParams.get().getPayMerchantId());
            contractSupplTransferReq.setRejectInfos(rejectInfos);
            ContractSupplTransferRes contractSupplTransferRes = contractSupplService.transferOnlySupplContent(contractSupplTransferReq);
            if (Objects.nonNull(contractSupplTransferRes) && Objects.nonNull(contractSupplTransferRes.getAuditTemplate())) {
                return contractSupplTransferRes.getAuditTemplate().stream()
                        .map(r -> new PayBizApplyStatusDetailRspDTO.RejectFieldInfo(
                                r.getRejectFieldList().stream().map(RejectAuditTemplateDTO.RejectField::getFieldName).collect(Collectors.toList()),
                                r.getContent(),
                                r.getSuggest()))
                        .collect(Collectors.toList());
            }
        } else if (apply.isDetailRiskControlReject()) {
            Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(apply.getMerchantSn(), ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());
            if (!acquirerParams.isPresent()) {
                log.error("未找到通联收银宝相关参数 {}", apply.getMerchantSn());
                return Collections.emptyList();
            }
            ContractSupplAuditResultResDTO contractSupplAuditResultResDTO = contractSupplAuditService.queryLastContractSupplAuditResultByProvider(acquirerParams.get().getPayMerchantId(), AcquirerTypeEnum.TONG_LIAN_V2.getValue());
            if (Objects.nonNull(contractSupplAuditResultResDTO) && Objects.nonNull(contractSupplAuditResultResDTO.getAuditTemplate())) {
                return contractSupplAuditResultResDTO.getAuditTemplate().stream()
                        .map(r -> new PayBizApplyStatusDetailRspDTO.RejectFieldInfo(
                                r.getRejectFieldList().stream().map(RejectAuditTemplateDTO.RejectField::getFieldName).collect(Collectors.toList()),
                                r.getContent(),
                                r.getSuggest()))
                        .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }
}
