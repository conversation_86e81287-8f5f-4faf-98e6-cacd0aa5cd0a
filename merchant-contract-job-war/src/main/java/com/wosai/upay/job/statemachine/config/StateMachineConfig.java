package com.wosai.upay.job.statemachine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 状态机配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "statemachine")
public class StateMachineConfig {

    /**
     * 是否启用状态机
     */
    private boolean enabled = true;

    /**
     * 是否启用状态转换日志
     */
    private boolean logEnabled = true;

    /**
     * 状态转换超时时间（毫秒）
     */
    private long transitionTimeout = 5000;

    /**
     * 是否启用状态转换监控
     */
    private boolean monitorEnabled = false;

    /**
     * 降级策略：当状态机失败时是否使用原有逻辑
     */
    private boolean fallbackEnabled = true;
}
