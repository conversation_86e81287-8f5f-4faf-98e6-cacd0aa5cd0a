package com.wosai.upay.job.biz.payBizApply;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.event.AcquirerChangeEvent;
import com.wosai.upay.job.refactor.event.ContractTaskEvent;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.tlV2.request.ElectUrlQueryRequest;
import com.wosai.upay.merchant.contract.model.tlV2.response.ElectUrlQueryResponse;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 通联支付业务申请事件监听器
 * 监听ContractTaskEvent和AcquirerChangeEvent，处理通联收银宝相关的状态变更
 *
 * <AUTHOR>
@Slf4j
@Component
public class TonglianV2PayBizApplyEventListener {

    @Autowired
    private PayBizApplyDAO payBizApplyDAO;

    @Autowired
    private PayBizApplyLogDAO payBizApplyLogDAO;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private TongLianV2Service tongLianV2Service;
    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private DirectStatusBiz directStatusBiz;

    /**
     * 监听合约任务事件
     */
    @org.springframework.context.event.EventListener
    public void handleContractTaskEvent(ContractTaskEvent event) {
        ContractTask contractTask = event.getContractTask();
        // 异步处理，避免影响主流程
        CompletableFuture.runAsync(() -> {
            try {
                handleContractTaskEventAsync(contractTask);
            } catch (Exception e) {
                log.error("处理ContractTaskEvent异常，taskId: {}, merchantSn: {}",
                        contractTask.getId(), contractTask.getMerchant_sn(), e);
            }
        });
    }

    /**
     * 监听收单机构切换事件
     */
    @org.springframework.context.event.EventListener
    public void handleAcquirerChangeEvent(AcquirerChangeEvent event) {
        String merchantSn = event.getMerchantSn();
        // 异步处理，避免影响主流程
        CompletableFuture.runAsync(() -> {
            try {
                handleAcquirerChangeEventAsync(event);
            } catch (Exception e) {
                log.error("处理AcquirerChangeEvent异常，merchantSn: {}", merchantSn, e);
            }
        });
    }

    /**
     * 异步处理合约任务事件
     */
    private void handleContractTaskEventAsync(ContractTask contractTask) {
        String merchantSn = contractTask.getMerchant_sn();
        Long taskId = contractTask.getId();
        Integer status = contractTask.getStatus();
        if (!ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue().equals(contractTask.getType()) || !WosaiStringUtils.contains(contractTask.getRule_group_id(), ContractRuleConstants.CHANGE_TO_TONGLIANV2_RULE_GROUP)) {
            return;
        }

        // 查询当前商户是否有进行中的通联申请单
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(merchantSn, DevCodeConstants.TONGLIAN_V2_DEV_CODE);
        if (!payBizApplyDO.isPresent() || !payBizApplyDO.get().isProcessing() || !payBizApplyDO.get().isDetailContractAuditing()) {
            log.debug("商户 {} 没有进件审核中的通联申请单，跳过处理", merchantSn);
            return;
        }
        PayBizApplyDO apply = payBizApplyDO.get();
        // 检查taskId是否一致
        if (!Objects.equals(contractTask.getId(), apply.getTaskId())) {
            log.debug("商户{}的通联申请单taskId不匹配，跳过处理。期望taskId: {}", merchantSn, taskId);
            return;
        }

        log.info("找到匹配的通联申请单，applyId: {}, taskId: {}, status: {}", apply.getId(), taskId, status);

        // 根据任务状态进行处理
        if (TaskStatus.SUCCESS.getVal().equals(status)) {
            handleTaskStatusWaitingLegalSign(apply);
        } else if (TaskStatus.FAIL.getVal().equals(status)) {
            handleTaskStatusEntryFailed(apply, contractTask.getResult());
        }
    }

    /**
     * 异步处理收单机构切换事件
     */
    private void handleAcquirerChangeEventAsync(AcquirerChangeEvent event) {
        String merchantSn = event.getMerchantSn();
        McAcquirerChange mcAcquirerChange = (McAcquirerChange) event.getSource();
        // 查询当前商户是否有进行中的通联申请单
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(merchantSn, DevCodeConstants.TONGLIAN_V2_DEV_CODE);
        if (!payBizApplyDO.isPresent() || !payBizApplyDO.get().isProcessing() || !payBizApplyDO.get().isDetailEnabling()) {
            log.debug("商户 {} 没有生效中的通联申请单，跳过处理", merchantSn);
            return;
        }
        PayBizApplyDO apply = payBizApplyDO.get();
        if (!Objects.equals(apply.getChangeAcquirerApplyId(), mcAcquirerChange.getApply_id())) {
            log.debug("商户{}的通联申请单changeAcquirerApplyId不匹配，跳过处理。期望changeAcquirerApplyId: {}", merchantSn, mcAcquirerChange.getApply_id());
            return;
        }
        if (event.isSuccess()) {
            handleAcquirerChangeSuccess(apply);
        } else {
            handleAcquirerChangeFailed(apply, event.getFailMsg());
        }
    }

    /**
     * 处理任务状态：待法人签约
     */
    private void handleTaskStatusWaitingLegalSign(PayBizApplyDO apply) {
        log.info("处理任务状态变更为待法人签约，applyId: {}", apply.getId());

        try {
            // 保存法人签约链接 url
            saveLegalSignUrl(apply);
            // 将申请单置为待法人签约状态
            payBizApplyDAO.completeCurrentStageAndMoveToNext(apply);
            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.ENTRY_AUDIT_PASS,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), apply.getDevCode(), DirectStatus.STATUS_PROCESS, null);

            log.info("成功将申请单{}置为待法人签约状态", apply.getId());

        } catch (Exception e) {
            log.error("处理待法人签约状态失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 处理任务状态：进件失败
     */
    private void handleTaskStatusEntryFailed(PayBizApplyDO apply, String failResult) {
        log.info("处理任务状态变更为进件失败，applyId: {}", apply.getId());

        try {
            payBizApplyDAO.updateToContractFailed(apply, failResult);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.ENTRY_AUDIT_FAIL,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.FAILED, PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED, failResult)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), apply.getDevCode(), DirectStatus.STATUS_BIZ_FAIL, failResult);

            log.info("成功将申请单{}置为进件失败状态", apply.getId());

        } catch (Exception e) {
            log.error("处理进件失败状态失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 处理收单机构切换成功
     */
    private void handleAcquirerChangeSuccess(PayBizApplyDO apply) {
        log.info("处理收单机构切换成功，applyId: {}", apply.getId());

        try {
            // 将申请单置为开通成功
            payBizApplyDAO.markApplyAsSuccess(apply);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.OPEN_SUCCESS,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.SUCCESS, PayBizApplyDetailStatusEnum.OPEN_SUCCESS)
            );

            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), apply.getDevCode(), DirectStatus.STATUS_SUCCESS, null);

            log.info("成功将申请单{}置为开通成功状态", apply.getId());

        } catch (Exception e) {
            log.error("处理收单机构切换成功失败，applyId: {}", apply.getId(), e);
        }
    }

    private void handleAcquirerChangeFailed(PayBizApplyDO apply, String failMsg) {
        log.info("处理收单机构切换失败，applyId: {} {}", apply.getId(), failMsg);

        try {
            // 将申请单置为开通成功
            payBizApplyDAO.updateToEnabledFailed(apply, failMsg);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.ENABLED_FAILED,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.ENABLE_FAILED, failMsg)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), apply.getDevCode(), DirectStatus.STATUS_BIZ_FAIL, failMsg);

            log.info("成功将申请单{}置为启用失败状态", apply.getId());

        } catch (Exception e) {
            log.error("处理收单机构切换成功失败，applyId: {}", apply.getId(), e);
        }
    }

    private void saveLegalSignUrl(PayBizApplyDO apply) {
        try {
            // 获取商户参数
            Optional<MerchantProviderParamsDO> acquirerParams = merchantProviderParamsDAO
                    .getMerchantProviderParamsByProviderAndPayway(apply.getMerchantSn(),
                            ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());

            if (!acquirerParams.isPresent()) {
                log.error("收单机构参数不存在，applyId: {}, merchantSn: {}", apply.getId(), apply.getMerchantSn());
                return;
            }

            MerchantProviderParamsDO merchantProviderParam = acquirerParams.get();

            // 构建通联参数
            TongLianV2Param tongLianV2Param = contractParamsBiz.buildContractParams(
                    merchantProviderParam.getProvider() + "",
                    merchantProviderParam.getPayway(),
                    merchantProviderParam.getChannelNo(),
                    TongLianV2Param.class);
            ElectUrlQueryRequest request = new ElectUrlQueryRequest();
            request.setAcquirerMerchantId(merchantProviderParam.getPayMerchantId());
            request.setType(ElectUrlQueryRequest.LEGAL_ELECT_SIGN);
            ContractResponse contractResponse = tongLianV2Service.queryElectUrlV2(request, tongLianV2Param);
            if (!contractResponse.isSuccess()) {
                log.error("查询签约链接失败，applyId: {}, response: {}", apply.getId(), contractResponse.getMessage());
                return;
            }

            ElectUrlQueryResponse queryResponse = JSON.parseObject(
                    JSON.toJSONString(contractResponse.getResponseParam()), ElectUrlQueryResponse.class);
            String url = queryResponse.getSybsignurl();

            if (StringUtils.hasText(url)) {
                // 保存签约链接
                payBizApplyDAO.saveLegalSignUrl(apply, url);
                log.info("成功查询并保存签约链接，applyId: {}", apply.getId());
            } else {
                log.warn("查询到的签约链接为空，applyId: {}", apply.getId());
            }

        } catch (Exception e) {
            log.error("查询签约链接异常，applyId: {}", apply.getId(), e);
        }
    }
}
