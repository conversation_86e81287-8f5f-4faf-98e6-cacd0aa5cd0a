package com.wosai.upay.job.biz.payBizApply;

import avro.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.dto.request.PayBizApplyReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizApplyStatusReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizEnabledSetReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignResendReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignUrlValidateReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusDetailRspDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusRspDTO;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowInfoBO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 支付业务开通申请处理服务抽象基类
 * 提供通用的处理逻辑，子类实现具体的业务逻辑
 *
 * <AUTHOR>
@Slf4j
public abstract class AbstractPayBizApplyHandleService implements PayBizApplyHandleService {

    @Autowired
    protected RedissonClient redissonClient;

    @Autowired
    private ContractStatusDAO contractStatusDAO;

    @Autowired
    protected PayBizApplyDAO payBizApplyDAO;

    @Autowired
    protected PayBizApplyLogDAO payBizApplyLogDAO;

    @Autowired
    protected ErrorCodeManageBiz errorCodeManageBiz;

    @Override
    public CuaCommonResultDTO handleApply(PayBizApplyReqDTO request) {
        String merchantSn = request.getMerchantSn();
        String devCode = request.getDevCode();
        String lockKey = String.format("payBizApply:%s_%s", merchantSn, devCode);

        // 分布式锁防止重复提交
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁
            if (!lock.tryLock()) {
                log.warn("获取分布式锁失败，merchantSn: {}, devCode: {}", merchantSn, devCode);
                return CuaCommonResultDTO.fail("系统繁忙，请稍后重试");
            }

            return doHandleApply(request);

        } catch (ContractBizException e) {
            log.error("支付业务申请业务异常，merchantSn: {}, devCode: {}, error: {}",
                    merchantSn, devCode, e.getMessage(), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("支付业务申请系统异常，merchantSn: {}, devCode: {}",
                    merchantSn, devCode, e);
            return CuaCommonResultDTO.fail("系统异常，请稍后重试");
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public PayBizApplyStatusDetailRspDTO getPayBizApplyStatusDetailInfo(PayBizApplyStatusReqDTO request) {
        try {
            PayBizApplyDO apply = checkPayBizApply(request.getMerchantSn(), request.getDevCode());
            return doBuildStatusDetailResponse(apply, request);
        } catch (ContractBizException e) {
            log.warn("查询支付业务申请状态详情业务异常: {}", JSON.toJSONString(request), e);
            throw e;
        } catch (Exception e) {
            log.error("查询支付业务申请状态详情系统异常: {}", JSON.toJSONString(request), e);
            throw new ContractBizException("系统异常");
        }
    }

    @Override
    public PayBizApplyStatusRspDTO getPayBizApplyStatusInfo(PayBizApplyStatusReqDTO request) {
        try {
            PayBizApplyDO apply = checkPayBizApply(request.getMerchantSn(), request.getDevCode());
            return doBuildPayBizApplyStatusInfo(apply, request);
        } catch (ContractBizException e) {
            log.warn("查询支付业务申请状态业务异常: {}", JSON.toJSONString(request), e);
            throw e;
        } catch (Exception e) {
            log.error("查询支付业务申请状态系统异常: {}", JSON.toJSONString(request), e);
            throw new ContractBizException("系统异常");
        }
    }

    @Override
    public CuaCommonResultDTO setPayBizEnabled(PayBizEnabledSetReqDTO request) {
        try {
            PayBizApplyDO apply = checkPayBizApply(request.getMerchantSn(), request.getDevCode());
            doSetPayBizEnabled(request, apply);
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.warn("查询支付业务申请状态详情业务异常: {}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("查询支付业务申请状态详情系统异常: {}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail("系统异常");
        }
    }

    @Override
    public CuaCommonResultDTO reSendSignUrl(PayBizSignResendReqDTO request) {
        try {
            PayBizApplyDO apply = checkPayBizApply(request.getMerchantSn(), request.getDevCode());
            doReSendSignUrl(request, apply);
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.warn("重新发送签约链接业务异常: {}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("重新发送签约链接系统异常: {}", JSON.toJSONString(request), e);
            return CuaCommonResultDTO.fail("系统异常");
        }
    }

    @Override
    public CuaCommonResultDTO validateSignUrl(PayBizSignUrlValidateReqDTO request) {
        try {
            PayBizApplyDO apply = checkPayBizApply(request.getMerchantSn(), request.getDevCode());
            return doValidateSignUrl(apply);
        } catch (ContractBizException e) {
            log.warn("校验签约链接过期业务异常，merchantSn: {}, devCode: {}, error: {}",
                    request.getMerchantSn(), request.getDevCode(), e.getMessage());
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("校验签约链接过期系统异常，merchantSn: {}, devCode: {}",
                    request.getMerchantSn(), request.getDevCode(), e);
            return CuaCommonResultDTO.fail("系统异常");
        }
    }

    private PayBizApplyDO checkPayBizApply(String merchantSn, String devCode) {
        // 查询申请单信息
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(merchantSn, devCode);
        if (!payBizApplyDO.isPresent()) {
            log.warn("未找到申请记录，merchantSn: {}, devCode: {}", merchantSn, devCode);
            throw new ContractBizException("未找到申请记录");
        }
        return payBizApplyDO.get();
    }

    /**
     * 执行申请处理的核心逻辑
     *
     * @param request 申请请求
     * @return 申请结果
     */
    protected CuaCommonResultDTO doHandleApply(PayBizApplyReqDTO request) {
        String merchantSn = request.getMerchantSn();
        String devCode = request.getDevCode();
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(merchantSn, devCode);
        PayBizApplyContext payBizApplyContext = PayBizApplyContext.create(request, payBizApplyDO.orElse(null));
        // 1.前置校验
        preCheck(payBizApplyContext);
        // 2. 自定义校验
        specialCheck(payBizApplyContext);
        if (payBizApplyContext.hasPayBizApply()) {
            // 4. 处理已存在的申请单
            return handleExistingApply(payBizApplyContext);
        } else {
            // 3. 创建新申请单
            return createNewApply(payBizApplyContext);
        }
    }

    private void preCheck(PayBizApplyContext context) {
        Optional<ContractStatusDO> contractStatusDOOptional = contractStatusDAO.getByMerchantSn(context.getMerchantSn());
        if (!contractStatusDOOptional.isPresent() || !Objects.equals(ContractStatus.STATUS_SUCCESS, contractStatusDOOptional.get().getStatus())) {
            throw new ContractBizException("商户间连扫码还未开通");
        }
        if (context.hasPayBizApply() && context.getPayBizApply().isSuccess()) {
            throw new ContractBizException("业务已开通,请勿重新提交");
        }
        if (context.hasPayBizApply() && context.getPayBizApply().isProcessing()) {
            throw new ContractBizException("业务开通中,请耐心等待");
        }
    }

    /**
     * 获取状态描述
     */
    protected String getStatusDescription(Integer status, Integer detailStatus) {
        PayBizApplyStatusEnum statusEnum = PayBizApplyStatusEnum.getByCode(status);
        PayBizApplyDetailStatusEnum detailStatusEnum = PayBizApplyDetailStatusEnum.getByCode(detailStatus);

        String statusDesc = statusEnum != null ? statusEnum.getDesc() : "未知状态";
        String detailStatusDesc = detailStatusEnum != null ? detailStatusEnum.getDesc() : "未知详细状态";

        return statusDesc + "-" + detailStatusDesc;
    }

    /**
     * 初始化流程节点
     * 每个实现类根据自己的业务特点初始化不同的流程节点
     *
     * @param context 上下文
     * @return 初始化的流程信息
     */
    public abstract PayBizApplyFlowInfoBO initFlowInfo(PayBizApplyContext context);

    /**
     * 自定义校验逻辑
     * 每个实现类根据自己的业务特点进行校验
     *
     * @param context 申请请求
     * @return 校验结果，抛出异常
     */
    public abstract void specialCheck(PayBizApplyContext context);

    /**
     * 处理已存在的申请单
     *
     * @param context 申请上下文
     * @return 申请结果
     */
    protected abstract CuaCommonResultDTO handleExistingApply(PayBizApplyContext context);

    /**
     * 创建新的申请单
     *
     * @param context 申请上下文
     * @return 申请结果
     */
    protected abstract CuaCommonResultDTO createNewApply(PayBizApplyContext context);

    /**
     * 构建状态详情响应
     *
     * @param apply 申请单
     * @param request 请求参数
     * @return 状态详情响应
     */
    public abstract PayBizApplyStatusDetailRspDTO doBuildStatusDetailResponse(PayBizApplyDO apply, PayBizApplyStatusReqDTO request);

    /**
     * 构建支付业务申请状态响应
     *
     * @param apply 申请单
     * @param request 请求参数
     * @return 支付业务申请状态响应
     */
    public abstract PayBizApplyStatusRspDTO doBuildPayBizApplyStatusInfo(PayBizApplyDO apply, PayBizApplyStatusReqDTO request);

    /**
     * 设置支付业务是否启用
     *
     * @param request 请求参数
     * @param apply   申请单
     */
    public abstract void doSetPayBizEnabled(PayBizEnabledSetReqDTO request, PayBizApplyDO apply);

    /**
     * 重新发送签约链接
     *
     * @param request 请求参数
     * @param apply   申请单
     */
    public void doReSendSignUrl(PayBizSignResendReqDTO request, PayBizApplyDO apply) {
        throw new ContractBizException("不支持重新发送签约链接");
    }

    /**
     * 校验签约链接是否过期
     *
     * @param apply 申请单
     * @return 校验结果
     */
    public CuaCommonResultDTO doValidateSignUrl(PayBizApplyDO apply) {
        throw new ContractBizException("不支持校验签约链接");
    }
}
