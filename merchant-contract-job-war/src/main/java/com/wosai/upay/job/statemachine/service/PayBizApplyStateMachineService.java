package com.wosai.upay.job.statemachine.service;

import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.statemachine.core.StateMachineResult;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyStateMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * PayBizApply状态机服务
 * 集成DAO操作、日志记录和DirectStatus更新
 */
@Slf4j
@Service
public class PayBizApplyStateMachineService {

    @Autowired
    private PayBizApplyStateMachine stateMachine;

    @Autowired
    private PayBizApplyDAO payBizApplyDAO;

    @Autowired
    private PayBizApplyLogDAO payBizApplyLogDAO;

    @Autowired
    private DirectStatusBiz directStatusBiz;

    /**
     * 执行状态转换
     * 包含完整的业务逻辑：状态转换、数据库更新、日志记录、DirectStatus更新
     */
    @Transactional(rollbackFor = Exception.class)
    public StateMachineResult<PayBizApplyDetailStatusEnum> fireEvent(PayBizApplyDO apply, PayBizApplyEvent event,
                                                                   String operator, String operatorId, String platform, String reason) {
        try {
            log.info("开始状态转换: 申请单ID={}, 当前状态={}, 事件={}, 操作人={}", 
                    apply.getId(), apply.getDetailStatusEnum(), event, operator);

            // 创建上下文
            PayBizApplyContext context = PayBizApplyContext.create(apply)
                    .withOperator(operator)
                    .withOperatorId(operatorId)
                    .withPlatform(platform)
                    .withReason(reason);

            // 获取当前状态
            PayBizApplyDetailStatusEnum currentState = apply.getDetailStatusEnum();

            // 执行状态转换
            StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachine.fireEvent(currentState, event, context);

            if (result.isSuccess()) {
                // 更新申请单状态
                updateApplyStatus(apply, result, context);

                // 记录操作日志
                logStateTransition(apply, result, event, context);

                // 更新DirectStatus
                updateDirectStatus(apply, result, context);

                log.info("状态转换成功: 申请单ID={}, {}→{}", apply.getId(), result.getFromState(), result.getToState());
            } else {
                log.warn("状态转换失败: 申请单ID={}, 错误={}", apply.getId(), result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("状态转换异常: 申请单ID={}, 事件={}", apply.getId(), event, e);
            return StateMachineResult.failure(apply.getDetailStatusEnum(), apply.getDetailStatusEnum(), 
                    "状态转换异常: " + e.getMessage());
        }
    }

    /**
     * 检查是否可以执行状态转换
     */
    public boolean canTransition(PayBizApplyDO apply, PayBizApplyEvent event) {
        try {
            PayBizApplyContext context = PayBizApplyContext.create(apply);
            return stateMachine.canTransition(apply.getDetailStatusEnum(), event, context);
        } catch (Exception e) {
            log.error("检查状态转换能力异常: 申请单ID={}, 事件={}", apply.getId(), event, e);
            return false;
        }
    }

    /**
     * 更新申请单状态
     */
    private void updateApplyStatus(PayBizApplyDO apply, StateMachineResult<PayBizApplyDetailStatusEnum> result, PayBizApplyContext context) {
        PayBizApplyDetailStatusEnum newDetailStatus = result.getToState();

        // 根据详细状态确定主状态
        Integer newMainStatus = determineMainStatus(newDetailStatus);

        // 更新申请单状态
        apply.updateStatus(newMainStatus, newDetailStatus.getCode(), newDetailStatus.getDesc());

        // 更新流程信息
        updateFlowInfo(apply, newDetailStatus);

        // 更新优先级时间
        apply.updatePriority(java.time.LocalDateTime.now());

        // 保存到数据库
        boolean updateSuccess = payBizApplyDAO.updateById(apply) > 0;
        if (!updateSuccess) {
            throw new RuntimeException("更新申请单状态失败，申请单ID: " + apply.getId());
        }

        log.debug("申请单状态更新成功: ID={}, 主状态={}, 详细状态={}", 
                apply.getId(), newMainStatus, newDetailStatus.getCode());
    }

    /**
     * 记录状态转换日志
     */
    private void logStateTransition(PayBizApplyDO apply, StateMachineResult<PayBizApplyDetailStatusEnum> result, 
                                  PayBizApplyEvent event, PayBizApplyContext context) {
        try {
            PayBizApplyLogDetailBO logDetail = PayBizApplyLogDetailBO.create(
                    determineMainStatus(result.getToState()),
                    result.getToState(),
                    context.getReason()
            );

            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    event.getOperationType(),
                    context.getOperator(),
                    context.getOperatorId(),
                    context.getPlatform(),
                    logDetail
            );

            log.debug("状态转换日志记录成功: 申请单ID={}, 事件={}", apply.getId(), event);

        } catch (Exception e) {
            log.error("记录状态转换日志失败: 申请单ID={}, 事件={}", apply.getId(), event, e);
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 更新DirectStatus
     */
    private void updateDirectStatus(PayBizApplyDO apply, StateMachineResult<PayBizApplyDetailStatusEnum> result, PayBizApplyContext context) {
        try {
            String directStatusValue;
            String directStatusReason = null;

            // 根据新状态确定DirectStatus值
            PayBizApplyDetailStatusEnum newState = result.getToState();
            if (newState == PayBizApplyDetailStatusEnum.OPEN_SUCCESS) {
                directStatusValue = DirectStatus.STATUS_SUCCESS;
            } else if (isFailedState(newState)) {
                directStatusValue = DirectStatus.STATUS_BIZ_FAIL;
                directStatusReason = context.getReason();
            } else {
                directStatusValue = DirectStatus.STATUS_PROCESS;
            }

            directStatusBiz.createOrUpdateDirectStatus(
                    apply.getMerchantSn(),
                    DevCodeConstants.TONGLIAN_V2_DEV_CODE,
                    directStatusValue,
                    directStatusReason
            );

            log.debug("DirectStatus更新成功: 商户={}, 状态={}", apply.getMerchantSn(), directStatusValue);

        } catch (Exception e) {
            log.error("更新DirectStatus失败: 申请单ID={}", apply.getId(), e);
            // DirectStatus更新失败不影响主流程
        }
    }

    /**
     * 根据详细状态确定主状态
     */
    private Integer determineMainStatus(PayBizApplyDetailStatusEnum detailStatus) {
        if (detailStatus == PayBizApplyDetailStatusEnum.OPEN_SUCCESS) {
            return PayBizApplyStatusEnum.SUCCESS.getCode();
        } else if (isFailedState(detailStatus)) {
            return PayBizApplyStatusEnum.FAILED.getCode();
        } else {
            return PayBizApplyStatusEnum.PROCESSING.getCode();
        }
    }

    /**
     * 判断是否为失败状态
     */
    private boolean isFailedState(PayBizApplyDetailStatusEnum detailStatus) {
        return detailStatus == PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED ||
               detailStatus == PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED ||
               detailStatus == PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED ||
               detailStatus == PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED ||
               detailStatus == PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED;
    }

    /**
     * 更新流程信息
     */
    private void updateFlowInfo(PayBizApplyDO apply, PayBizApplyDetailStatusEnum newDetailStatus) {
        // 这里可以根据需要更新流程信息
        // 暂时保持原有逻辑
    }
}
