package com.wosai.upay.job.statemachine.service;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.job.statemachine.core.StateMachineResult;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyStateMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支付业务申请状态机服务
 * 封装状态机操作，提供业务层接口
 */
@Slf4j
@Service
public class PayBizApplyStateMachineService {

    @Autowired
    private PayBizApplyStateMachine stateMachine;

    @Autowired
    private PayBizApplyDAO payBizApplyDAO;

    @Autowired
    private PayBizApplyLogDAO payBizApplyLogDAO;

    /**
     * 执行状态转换
     *
     * @param apply     申请单
     * @param event     触发事件
     * @param operator  操作人
     * @param operatorId 操作人ID
     * @param platform  操作平台
     * @param reason    转换原因
     * @return 转换结果
     */
    @Transactional(rollbackFor = Exception.class)
    public StateMachineResult<PayBizApplyDetailStatusEnum> fireEvent(PayBizApplyDO apply, PayBizApplyEvent event,
                                                                     String operator, String operatorId, String platform, String reason) {
        // 创建上下文
        PayBizApplyContext context = PayBizApplyContext.create(apply, operator, operatorId, platform)
                .withReason(reason);

        // 获取当前状态
        PayBizApplyDetailStatusEnum currentState = PayBizApplyDetailStatusEnum.getByCode(apply.getDetailStatus());
        if (currentState == null) {
            log.error("无效的申请单状态: {}", apply.getDetailStatus());
            return StateMachineResult.failure(null, "无效的申请单状态");
        }

        // 执行状态转换
        StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachine.fireEvent(currentState, event, context);

        if (result.isSuccess()) {
            // 状态转换成功，更新数据库
            updateApplyStatus(apply, result, context);
            
            // 记录操作日志
            logStateTransition(apply, result, event, context);
        } else {
            log.error("状态转换失败: 申请单ID={}, 当前状态={}, 事件={}, 错误={}",
                    apply.getId(), currentState, event, result.getErrorMessage());
        }

        return result;
    }

    /**
     * 检查是否可以执行指定的状态转换
     */
    public boolean canTransition(PayBizApplyDO apply, PayBizApplyEvent event) {
        PayBizApplyDetailStatusEnum currentState = PayBizApplyDetailStatusEnum.getByCode(apply.getDetailStatus());
        if (currentState == null) {
            return false;
        }

        PayBizApplyContext context = PayBizApplyContext.create(apply);
        return stateMachine.canTransition(currentState, event, context);
    }

    /**
     * 更新申请单状态
     */
    private void updateApplyStatus(PayBizApplyDO apply, StateMachineResult<PayBizApplyDetailStatusEnum> result, PayBizApplyContext context) {
        PayBizApplyDetailStatusEnum newDetailStatus = result.getToState();

        // 根据详细状态确定主状态
        Integer newMainStatus = determineMainStatus(newDetailStatus);

        // 更新申请单状态
        apply.updateStatus(newMainStatus, newDetailStatus.getCode(), newDetailStatus.getDesc());

        // 更新流程信息
        updateFlowInfo(apply, newDetailStatus);

        // 更新优先级时间
        apply.updatePriority(java.time.LocalDateTime.now());

        // 保存到数据库
        boolean updateSuccess = payBizApplyDAO.updateById(apply) > 0;
        if (!updateSuccess) {
            throw new RuntimeException("更新申请单状态失败，申请单ID: " + apply.getId());
        }

        log.info("申请单状态已更新: ID={}, 状态={}→{}",
                apply.getId(), result.getFromState(), result.getToState());
    }

    /**
     * 更新流程信息
     */
    private void updateFlowInfo(PayBizApplyDO apply, PayBizApplyDetailStatusEnum newStatus) {
        try {
            // 标记当前节点为完成
            apply.markFlowNodeAsFinished(newStatus.getCode());
        } catch (Exception e) {
            log.warn("更新流程信息失败，申请单ID: {}, 状态: {}", apply.getId(), newStatus, e);
        }
    }

    /**
     * 根据详细状态确定主状态
     */
    private Integer determineMainStatus(PayBizApplyDetailStatusEnum detailStatus) {
        if (PayBizApplyDetailStatusEnum.isSuccessStatus(detailStatus.getCode())) {
            return PayBizApplyStatusEnum.SUCCESS.getCode();
        } else if (PayBizApplyDetailStatusEnum.isFailedStatus(detailStatus.getCode())) {
            return PayBizApplyStatusEnum.FAILED.getCode();
        } else {
            return PayBizApplyStatusEnum.PROCESSING.getCode();
        }
    }

    /**
     * 记录状态转换日志
     */
    private void logStateTransition(PayBizApplyDO apply, StateMachineResult<PayBizApplyDetailStatusEnum> result,
                                   PayBizApplyEvent event, PayBizApplyContext context) {
        PayBizApplyOperationEnum operation = mapEventToOperation(event);
        
        PayBizApplyLogDetailBO logDetail = PayBizApplyLogDetailBO.create(
                determineMainStatus(result.getToState()),
                result.getToState()
        );
        
        payBizApplyLogDAO.logOperationWithJsonDetail(
                String.valueOf(apply.getId()),
                operation,
                context.getOperator(),
                context.getOperatorId(),
                context.getPlatform(),
                logDetail
        );
    }

    /**
     * 将事件映射到操作类型
     */
    private PayBizApplyOperationEnum mapEventToOperation(PayBizApplyEvent event) {
        switch (event) {
            case CONTRACT_AUDIT_SUCCESS:
            case CONTRACT_AUDIT_FAIL:
                return PayBizApplyOperationEnum.CONTRACT_AUDIT;
            case LEGAL_SIGN_SUCCESS:
            case LEGAL_SIGN_FAIL:
            case LEGAL_SIGN_EXPIRE:
                return PayBizApplyOperationEnum.LEGAL_SIGN;
            case SETTLEMENT_SIGN_SUCCESS:
            case SETTLEMENT_SIGN_FAIL:
            case SETTLEMENT_SIGN_EXPIRE:
                return PayBizApplyOperationEnum.SETTLEMENT_SIGN;
            case COMPLIANCE_AUDIT_SUCCESS:
            case COMPLIANCE_AUDIT_FAIL:
                return PayBizApplyOperationEnum.COMPLIANCE_AUDIT;
            case ENABLE_REQUEST:
            case ENABLE_SUCCESS:
            case ENABLE_FAIL:
                return PayBizApplyOperationEnum.ENABLE_OPERATION;
            case ROLLBACK:
                return PayBizApplyOperationEnum.ROLLBACK;
            case MANUAL_INTERVENTION:
                return PayBizApplyOperationEnum.MANUAL_INTERVENTION;
            default:
                return PayBizApplyOperationEnum.OTHER;
        }
    }
}
