package com.wosai.upay.job.statemachine.paybizapply;

import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付业务申请状态机上下文
 * 包含状态转换所需的所有信息
 */
@Data
public class PayBizApplyContext {

    /**
     * 申请单实体
     */
    private PayBizApplyDO apply;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户ID
     */
    private String operatorId;

    /**
     * 操作平台
     */
    private String platform;

    /**
     * 事件触发原因
     */
    private String reason;

    /**
     * 扩展参数
     */
    private Map<String, Object> parameters = new HashMap<>();

    /**
     * 创建上下文
     */
    public static PayBizApplyContext create(PayBizApplyDO apply) {
        PayBizApplyContext context = new PayBizApplyContext();
        context.setApply(apply);
        context.setOperator("SYSTEM");
        context.setOperatorId("SYSTEM");
        context.setPlatform("SYSTEM");
        return context;
    }

    /**
     * 创建上下文（带操作人信息）
     */
    public static PayBizApplyContext create(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        PayBizApplyContext context = new PayBizApplyContext();
        context.setApply(apply);
        context.setOperator(operator);
        context.setOperatorId(operatorId);
        context.setPlatform(platform);
        return context;
    }

    /**
     * 添加参数
     */
    public PayBizApplyContext addParameter(String key, Object value) {
        this.parameters.put(key, value);
        return this;
    }

    /**
     * 获取参数
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        Object value = parameters.get(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }

    /**
     * 获取参数（带默认值）
     */
    public <T> T getParameter(String key, Class<T> type, T defaultValue) {
        T value = getParameter(key, type);
        return value != null ? value : defaultValue;
    }

    /**
     * 设置原因
     */
    public PayBizApplyContext withReason(String reason) {
        this.reason = reason;
        return this;
    }
}
