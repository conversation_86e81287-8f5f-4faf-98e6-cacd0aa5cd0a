package com.wosai.upay.job.statemachine.paybizapply;

import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付业务申请状态机上下文
 * 包含状态转换所需的所有信息
 */
@Data
public class PayBizApplyContext {

    /**
     * 申请单实体
     */
    private PayBizApplyDO apply;

    /**
     * 操作用户
     */
    private String operator;

    /**
     * 操作用户ID
     */
    private String operatorId;

    /**
     * 操作平台
     */
    private String platform;

    /**
     * 事件触发原因
     */
    private String reason;

    /**
     * 扩展参数
     */
    private Map<String, Object> parameters = new HashMap<>();

    /**
     * 创建上下文
     */
    public static PayBizApplyContext create(PayBizApplyDO apply) {
        PayBizApplyContext context = new PayBizApplyContext();
        context.setApply(apply);
        context.setOperator("SYSTEM");
        context.setOperatorId("SYSTEM");
        context.setPlatform("SYSTEM");
        return context;
    }

    /**
     * 创建上下文（带操作人信息）
     */
    public static PayBizApplyContext create(PayBizApplyDO apply, String operator, String operatorId, String platform) {
        PayBizApplyContext context = new PayBizApplyContext();
        context.setApply(apply);
        context.setOperator(operator);
        context.setOperatorId(operatorId);
        context.setPlatform(platform);
        return context;
    }

    /**
     * 添加参数
     */
    public PayBizApplyContext addParameter(String key, Object value) {
        this.parameters.put(key, value);
        return this;
    }

    /**
     * 获取参数
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        Object value = parameters.get(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }

    /**
     * 获取参数（带默认值）
     */
    public <T> T getParameter(String key, Class<T> type, T defaultValue) {
        T value = getParameter(key, type);
        return value != null ? value : defaultValue;
    }

    /**
     * 设置原因
     */
    public PayBizApplyContext withReason(String reason) {
        this.reason = reason;
        return this;
    }

    // ==================== 业务数据便捷方法 ====================

    /**
     * 设置任务ID
     */
    public PayBizApplyContext withTaskId(Long taskId) {
        return addParameter("taskId", taskId);
    }

    /**
     * 获取任务ID
     */
    public Long getTaskId() {
        return getParameter("taskId", Long.class);
    }

    /**
     * 设置收单机构切换申请ID
     */
    public PayBizApplyContext withChangeAcquirerApplyId(String applyId) {
        return addParameter("changeAcquirerApplyId", applyId);
    }

    /**
     * 获取收单机构切换申请ID
     */
    public String getChangeAcquirerApplyId() {
        return getParameter("changeAcquirerApplyId", String.class);
    }

    /**
     * 设置签约链接
     */
    public PayBizApplyContext withSignUrl(String signUrl) {
        return addParameter("signUrl", signUrl);
    }

    /**
     * 获取签约链接
     */
    public String getSignUrl() {
        return getParameter("signUrl", String.class);
    }

    /**
     * 设置失败结果
     */
    public PayBizApplyContext withFailResult(String failResult) {
        return addParameter("failResult", failResult);
    }

    /**
     * 获取失败结果
     */
    public String getFailResult() {
        return getParameter("failResult", String.class);
    }

    /**
     * 设置处理详情
     */
    public PayBizApplyContext withHandleDetail(String handleDetail) {
        return addParameter("handleDetail", handleDetail);
    }

    /**
     * 获取处理详情
     */
    public String getHandleDetail() {
        return getParameter("handleDetail", String.class);
    }
}
