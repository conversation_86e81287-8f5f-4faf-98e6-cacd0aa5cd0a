package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 进件审核失败状态
 * 对应状态流转图中的 CONTRACT_AUDIT_FAILED
 */
@Slf4j
@Component
public class ContractAuditFailedState extends PayBizApplyState {

    public ContractAuditFailedState() {
        super(PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED,
                PayBizApplyEvent.RESUBMIT_APPLY);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case RESUBMIT_APPLY:
                // 重新提交申请 -> 进件审核中
                log.info("重新提交申请，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.CONTRACT_AUDITING;

            default:
                log.warn("进件审核失败状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入进件审核失败状态，申请单ID: {}", context.getApply().getId());
        
        // 更新失败结果
        String failResult = context.getFailResult();
        if (failResult != null) {
            context.getApply().updateResult(failResult);
        }
    }

    @Override
    public boolean isFinalState() {
        return true; // 这是一个失败的终态
    }
}
