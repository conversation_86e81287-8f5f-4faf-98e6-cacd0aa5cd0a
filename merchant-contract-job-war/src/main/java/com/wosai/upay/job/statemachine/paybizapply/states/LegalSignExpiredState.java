package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法人签约协议失效状态
 * 对应状态流转图中的 LEGAL_SIGN_EXPIRED
 */
@Slf4j
@Component
public class LegalSignExpiredState extends PayBizApplyState {

    public LegalSignExpiredState() {
        super(PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED,
                PayBizApplyEvent.RESIGN);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case RESIGN:
                // 重新发送签约链接 -> 待法人签约
                log.info("重新发送法人签约链接，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN;

            default:
                log.warn("法人签约协议失效状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入法人签约协议失效状态，申请单ID: {}", context.getApply().getId());
    }
}
