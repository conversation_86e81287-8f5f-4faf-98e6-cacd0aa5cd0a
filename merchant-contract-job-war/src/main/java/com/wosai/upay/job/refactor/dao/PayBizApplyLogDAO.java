package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.PayBizApplyLogDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyLogDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付业务操作流水表数据库访问层 {@link PayBizApplyLogDO}
 * 对PayBizApplyLogDynamicMapper层做出简单封装 {@link PayBizApplyLogDynamicMapper}
 *
 * <AUTHOR>
@Repository
public class PayBizApplyLogDAO extends AbstractBaseDAO<PayBizApplyLogDO, PayBizApplyLogDynamicMapper> {

    public PayBizApplyLogDAO(SqlSessionFactory sqlSessionFactory, PayBizApplyLogDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据业务ID查询操作流水记录
     *
     * @param bizId 业务主键值
     * @return 操作流水记录列表
     */
    public List<PayBizApplyLogDO> selectByBizId(String bizId) {
        LambdaQueryWrapper<PayBizApplyLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyLogDO::getBizId, bizId)
                   .orderByDesc(PayBizApplyLogDO::getCtime);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 根据业务ID和操作类型查询操作流水记录
     *
     * @param bizId 业务主键值
     * @param operatorType 操作类型
     * @return 操作流水记录列表
     */
    public List<PayBizApplyLogDO> selectByBizIdAndOperatorType(String bizId, String operatorType) {
        LambdaQueryWrapper<PayBizApplyLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyLogDO::getBizId, bizId)
                   .eq(PayBizApplyLogDO::getOperatorType, operatorType)
                   .orderByDesc(PayBizApplyLogDO::getCtime);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 根据操作人ID查询操作流水记录
     *
     * @param operatorId 操作人ID
     * @param limit 查询限制数量
     * @return 操作流水记录列表
     */
    public List<PayBizApplyLogDO> selectByOperatorId(String operatorId, Integer limit) {
        LambdaQueryWrapper<PayBizApplyLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyLogDO::getOperatorId, operatorId)
                   .orderByDesc(PayBizApplyLogDO::getCtime)
                   .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 根据时间范围查询操作流水记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 查询限制数量
     * @return 操作流水记录列表
     */
    public List<PayBizApplyLogDO> selectByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        LambdaQueryWrapper<PayBizApplyLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(PayBizApplyLogDO::getCtime, startTime)
                   .le(PayBizApplyLogDO::getCtime, endTime)
                   .orderByDesc(PayBizApplyLogDO::getCtime)
                   .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 根据操作平台查询操作流水记录
     *
     * @param platform 操作平台
     * @param limit 查询限制数量
     * @return 操作流水记录列表
     */
    public List<PayBizApplyLogDO> selectByPlatform(String platform, Integer limit) {
        LambdaQueryWrapper<PayBizApplyLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyLogDO::getPlatform, platform)
                   .orderByDesc(PayBizApplyLogDO::getCtime)
                   .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 保存操作流水记录
     *
     * @param payBizApplyLogDO 操作流水记录
     */
    public void save(PayBizApplyLogDO payBizApplyLogDO) {
        Integer affectedRows = insertOne(payBizApplyLogDO);
        if (affectedRows < 1) {
            throw new ContractBizException("保存支付业务操作流水记录失败，请重试");
        }
    }

    /**
     * 记录插入操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     */
    public void logInsertOperation(String bizId, String operator, String operatorId, String platform, String detail) {
        PayBizApplyLogDO log = PayBizApplyLogDO.createInsertLog(bizId, operator, operatorId, platform, detail);
        save(log);
    }

    /**
     * 记录更新操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     */
    public void logUpdateOperation(String bizId, String operator, String operatorId, String platform, String detail) {
        PayBizApplyLogDO log = PayBizApplyLogDO.createUpdateLog(bizId, operator, operatorId, platform, detail);
        save(log);
    }

    /**
     * 记录提交操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     */
    public void logSubmitOperation(String bizId, String operator, String operatorId, String platform, String detail) {
        PayBizApplyLogDO log = PayBizApplyLogDO.createSubmitLog(bizId, operator, operatorId, platform, detail);
        save(log);
    }

    /**
     * 记录审核操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     */
    public void logAuditOperation(String bizId, String operator, String operatorId, String platform, String detail) {
        PayBizApplyLogDO log = PayBizApplyLogDO.createAuditLog(bizId, operator, operatorId, platform, detail);
        save(log);
    }

    /**
     * 记录自定义操作流水
     *
     * @param bizId 业务主键值
     * @param operatorType 操作类型
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     */
    public void logCustomOperation(String bizId, String operatorType, String operator, String operatorId, 
                                  String platform, String detail) {
        PayBizApplyLogDO log = PayBizApplyLogDO.createNewLog(bizId, operatorType, operator, operatorId, platform, detail);
        save(log);
    }

    /**
     * 记录操作流水（使用JSON格式的详情，带自定义描述）
     *
     * @param bizId 业务主键值
     * @param operation 操作类型枚举
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detailBO 日志详情BO
     */
    public void logOperationWithJsonDetail(String bizId, PayBizApplyOperationEnum operation, String operator,
                                          String operatorId, String platform, PayBizApplyLogDetailBO detailBO) {
        // 创建日志记录（使用英文操作类型）
        PayBizApplyLogDO log = PayBizApplyLogDO.createNewLog(bizId, operation.getOperationType(), operator, operatorId, platform, detailBO.toJsonString());
        save(log);
    }
}
