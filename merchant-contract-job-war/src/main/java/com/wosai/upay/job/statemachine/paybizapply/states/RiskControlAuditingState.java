package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风控审核中状态
 * 对应状态流转图中的 RISK_CONTROL_AUDITING
 */
@Slf4j
@Component
public class RiskControlAuditingState extends PayBizApplyState {

    public RiskControlAuditingState() {
        super(PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING,
                PayBizApplyEvent.RISK_CONTROL_SUCCESS,
                PayBizApplyEvent.COMPLIANCE_AUDIT_FAIL);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case RISK_CONTROL_SUCCESS:
                // 风控审核通过 -> 合规补录审核中
                log.info("风控审核通过，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING;

            case COMPLIANCE_AUDIT_FAIL:
                // 风控审核驳回 -> 风控审核驳回
                log.info("风控审核驳回，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED;

            default:
                log.warn("风控审核中状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入风控审核中状态，申请单ID: {}", context.getApply().getId());
    }
}
