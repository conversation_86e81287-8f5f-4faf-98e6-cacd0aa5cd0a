package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风控审核中状态
 */
@Slf4j
@Component
public class RiskControlAuditingState extends PayBizApplyState {

    public RiskControlAuditingState() {
        super(PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING,
                PayBizApplyEvent.RISK_AUDIT_SUCCESS,
                PayBizApplyEvent.RISK_AUDIT_FAIL,
                PayBizApplyEvent.SUPPLEMENT_REQUIRED);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case RISK_AUDIT_SUCCESS:
                // 风控审核成功 -> 合规补录审核中
                log.info("风控审核成功，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING;

            case RISK_AUDIT_FAIL:
                // 风控审核失败 -> 风控审核驳回
                log.info("风控审核失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED;

            case SUPPLEMENT_REQUIRED:
                // 需要补录 -> 待补录
                log.info("需要补录信息，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_SUPPLEMENT;

            default:
                log.warn("风控审核中状态不支持事件: {}", event);
                return null;
        }
    }
}
