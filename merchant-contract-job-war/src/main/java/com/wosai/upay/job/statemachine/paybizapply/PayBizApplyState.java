package com.wosai.upay.job.statemachine.paybizapply;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.core.State;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 支付业务申请状态基类
 * 提供状态的通用实现
 */
@Slf4j
public abstract class PayBizApplyState implements State<PayBizApplyDetailStatusEnum, PayBizApplyEvent, PayBizApplyContext> {

    /**
     * 状态对应的枚举
     */
    protected final PayBizApplyDetailStatusEnum statusEnum;

    /**
     * 支持的事件列表
     */
    protected final List<PayBizApplyEvent> supportedEvents;

    public PayBizApplyState(PayBizApplyDetailStatusEnum statusEnum, PayBizApplyEvent... supportedEvents) {
        this.statusEnum = statusEnum;
        this.supportedEvents = new ArrayList<>(Arrays.asList(supportedEvents));
    }

    @Override
    public PayBizApplyDetailStatusEnum getStateId() {
        return statusEnum;
    }

    @Override
    public String getStateName() {
        return statusEnum.getDesc();
    }

    @Override
    public String getStateDescription() {
        return statusEnum.getDescription();
    }

    @Override
    public List<PayBizApplyEvent> getSupportedEvents() {
        return new ArrayList<>(supportedEvents);
    }

    @Override
    public boolean canHandle(PayBizApplyEvent event, PayBizApplyContext context) {
        // 基础检查：是否支持该事件
        if (!supportedEvents.contains(event)) {
            return false;
        }

        // 子类可以重写此方法添加额外的条件检查
        return canHandleInternal(event, context);
    }

    /**
     * 子类重写此方法实现具体的条件检查
     */
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        return true;
    }

    @Override
    public PayBizApplyDetailStatusEnum handleEvent(PayBizApplyEvent event, PayBizApplyContext context) {
        if (!canHandle(event, context)) {
            log.warn("状态 {} 不能处理事件 {}", getStateId(), event);
            return null;
        }

        return handleEventInternal(event, context);
    }

    /**
     * 子类实现具体的事件处理逻辑
     */
    protected abstract PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context);

    @Override
    public void onEnter(PayBizApplyContext context) {
        log.debug("进入状态: {} - {}", getStateId(), getStateName());
        onEnterInternal(context);
    }

    @Override
    public void onExit(PayBizApplyContext context) {
        log.debug("离开状态: {} - {}", getStateId(), getStateName());
        onExitInternal(context);
    }

    /**
     * 子类可以重写此方法实现进入状态时的动作
     */
    protected void onEnterInternal(PayBizApplyContext context) {
        // 默认空实现
    }

    /**
     * 子类可以重写此方法实现离开状态时的动作
     */
    protected void onExitInternal(PayBizApplyContext context) {
        // 默认空实现
    }

    /**
     * 检查上下文中的申请单状态是否与当前状态匹配
     */
    protected boolean validateCurrentState(PayBizApplyContext context) {
        if (context.getApply() == null) {
            log.warn("上下文中的申请单为空");
            return false;
        }

        PayBizApplyDetailStatusEnum currentStatus = PayBizApplyDetailStatusEnum.getByCode(context.getApply().getDetailStatus());
        if (!getStateId().equals(currentStatus)) {
            log.warn("申请单当前状态 {} 与期望状态 {} 不匹配", currentStatus, getStateId());
            return false;
        }

        return true;
    }
}
