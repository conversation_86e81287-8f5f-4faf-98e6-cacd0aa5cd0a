package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 进件审核中状态
 * 对应状态流转图中的 CONTRACT_AUDITING
 */
@Slf4j
@Component
public class ContractAuditingState extends PayBizApplyState {

    public ContractAuditingState() {
        super(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.ENTRY_AUDIT_PASS,
                PayBizApplyEvent.ENTRY_AUDIT_FAIL);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case ENTRY_AUDIT_PASS:
                // 进件审核通过 -> 待法人签约
                log.info("进件审核通过，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN;

            case ENTRY_AUDIT_FAIL:
                // 进件审核失败 -> 进件审核失败
                log.info("进件审核失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED;

            default:
                log.warn("进件审核中状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入进件审核中状态，申请单ID: {}", context.getApply().getId());
        
        // 保存任务ID（如果有）
        Long taskId = context.getTaskId();
        if (taskId != null) {
            context.getApply().updateTaskId(taskId);
            log.info("保存任务ID: {}, 申请单ID: {}", taskId, context.getApply().getId());
        }
    }

    @Override
    protected void onExitInternal(PayBizApplyContext context) {
        log.info("离开进件审核中状态，申请单ID: {}", context.getApply().getId());
    }
}
