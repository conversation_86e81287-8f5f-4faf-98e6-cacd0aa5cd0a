package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 进件审核中状态
 */
@Slf4j
@Component
public class ContractAuditingState extends PayBizApplyState {

    public ContractAuditingState() {
        super(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.CONTRACT_AUDIT_SUCCESS,
                PayBizApplyEvent.CONTRACT_AUDIT_FAIL);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case CONTRACT_AUDIT_SUCCESS:
                // 进件审核成功 -> 待法人签约
                log.info("进件审核成功，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN;

            case CONTRACT_AUDIT_FAIL:
                // 进件审核失败 -> 进件审核失败
                log.info("进件审核失败，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED;

            default:
                log.warn("进件审核中状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        // 验证当前状态
        if (!validateCurrentState(context)) {
            return false;
        }

        // 可以添加其他业务条件检查
        // 例如：检查审核结果是否已经返回等

        return true;
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        // 进入进件审核中状态时的动作
        log.info("开始进件审核，申请单ID: {}", context.getApply().getId());

        // 保存任务ID（如果有）
        Long taskId = context.getTaskId();
        if (taskId != null) {
            context.getApply().updateTaskId(taskId);
            log.info("保存任务ID: {}, 申请单ID: {}", taskId, context.getApply().getId());
        }
    }

    @Override
    protected void onExitInternal(PayBizApplyContext context) {
        // 离开进件审核中状态时的动作
        log.info("完成进件审核，申请单ID: {}", context.getApply().getId());

        // 标记当前流程节点为完成
        context.getApply().markCurrentFlowNodeAsFinished();
    }
}
