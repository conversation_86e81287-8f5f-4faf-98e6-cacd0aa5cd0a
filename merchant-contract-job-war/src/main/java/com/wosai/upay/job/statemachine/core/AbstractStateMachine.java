package com.wosai.upay.job.statemachine.core;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抽象状态机实现
 * 提供状态机的基础功能
 *
 * @param <S> 状态类型
 * @param <E> 事件类型
 * @param <C> 上下文类型
 */
@Slf4j
public abstract class AbstractStateMachine<S, E, C> implements StateMachine<S, E, C> {

    /**
     * 状态映射表
     */
    protected final Map<S, State<S, E, C>> stateMap = new HashMap<>();

    /**
     * 状态转换监听器列表
     */
    protected final List<StateTransitionListener<S, E, C>> listeners = new ArrayList<>();

    /**
     * 注册状态
     *
     * @param state 状态实例
     */
    protected void registerState(State<S, E, C> state) {
        stateMap.put(state.getStateId(), state);
        log.debug("注册状态: {} - {}", state.getStateId(), state.getStateName());
    }

    /**
     * 添加状态转换监听器
     *
     * @param listener 监听器
     */
    public void addStateTransitionListener(StateTransitionListener<S, E, C> listener) {
        listeners.add(listener);
    }

    @Override
    public StateMachineResult<S> fireEvent(S currentState, E event, C context) {
        log.debug("状态机 {} 处理事件: 当前状态={}, 事件={}", getName(), currentState, event);

        State<S, E, C> state = stateMap.get(currentState);
        if (state == null) {
            String errorMsg = String.format("未找到状态: %s", currentState);
            log.error(errorMsg);
            return StateMachineResult.failure(currentState, errorMsg);
        }

        // 检查是否可以处理该事件
        if (!state.canHandle(event, context)) {
            String errorMsg = String.format("状态 %s 不能处理事件 %s", currentState, event);
            log.warn(errorMsg);
            return StateMachineResult.failure(currentState, errorMsg);
        }

        try {
            // 触发状态转换前的监听器
            notifyBeforeTransition(currentState, event, context);

            // 执行状态转换
            S targetState = state.handleEvent(event, context);
            if (targetState == null) {
                String errorMsg = String.format("状态转换失败: %s -> %s", currentState, event);
                log.error(errorMsg);
                return StateMachineResult.failure(currentState, errorMsg);
            }

            // 执行状态转换动作
            if (!currentState.equals(targetState)) {
                // 离开当前状态
                state.onExit(context);

                // 进入目标状态
                State<S, E, C> targetStateObj = stateMap.get(targetState);
                if (targetStateObj != null) {
                    targetStateObj.onEnter(context);
                }
            }

            // 触发状态转换后的监听器
            notifyAfterTransition(currentState, targetState, event, context);

            StateMachineResult<S> result = StateMachineResult.success(currentState, targetState);
            log.debug("状态转换成功: {} -> {}", currentState, targetState);
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("状态转换异常: %s", e.getMessage());
            log.error(errorMsg, e);
            return StateMachineResult.failure(currentState, errorMsg);
        }
    }

    @Override
    public boolean canTransition(S fromState, E event, C context) {
        State<S, E, C> state = stateMap.get(fromState);
        return state != null && state.canHandle(event, context);
    }

    @Override
    public List<S> getAvailableTransitions(S fromState, C context) {
        List<S> availableStates = new ArrayList<>();
        State<S, E, C> state = stateMap.get(fromState);
        if (state != null) {
            for (E event : state.getSupportedEvents()) {
                if (state.canHandle(event, context)) {
                    S targetState = state.handleEvent(event, context);
                    if (targetState != null && !availableStates.contains(targetState)) {
                        availableStates.add(targetState);
                    }
                }
            }
        }
        return availableStates;
    }

    /**
     * 通知状态转换前的监听器
     */
    private void notifyBeforeTransition(S fromState, E event, C context) {
        for (StateTransitionListener<S, E, C> listener : listeners) {
            try {
                listener.beforeTransition(fromState, event, context);
            } catch (Exception e) {
                log.warn("状态转换前监听器执行异常", e);
            }
        }
    }

    /**
     * 通知状态转换后的监听器
     */
    private void notifyAfterTransition(S fromState, S toState, E event, C context) {
        for (StateTransitionListener<S, E, C> listener : listeners) {
            try {
                listener.afterTransition(fromState, toState, event, context);
            } catch (Exception e) {
                log.warn("状态转换后监听器执行异常", e);
            }
        }
    }

    /**
     * 状态转换监听器接口
     */
    public interface StateTransitionListener<S, E, C> {
        /**
         * 状态转换前回调
         */
        default void beforeTransition(S fromState, E event, C context) {
        }

        /**
         * 状态转换后回调
         */
        default void afterTransition(S fromState, S toState, E event, C context) {
        }
    }
}
