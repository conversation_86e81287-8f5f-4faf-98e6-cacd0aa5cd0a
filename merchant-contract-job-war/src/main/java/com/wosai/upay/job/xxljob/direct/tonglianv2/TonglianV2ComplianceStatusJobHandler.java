package com.wosai.upay.job.xxljob.direct.tonglianv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.notification.req.ContractSupplTransferReq;
import com.wosai.notification.service.ContractSupplService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.job.xxljob.context.TonglianV2Context;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.enume.tonglianv2.TonglianV2ComplianceRejectEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.tlV2.response.ComplianceQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通联收银宝V2合规性审核状态查询定时任务
 *
 * <AUTHOR>
 * @date 2025/9/15
 */
@Slf4j
@Component("TonglianV2ComplianceStatusJobHandler")
public class TonglianV2ComplianceStatusJobHandler extends AbstractTonglianV2JobHandler {

    @Autowired
    private ContractSupplService contractSupplService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Override
    public String getLockKey() {
        return "TonglianV2ComplianceStatusJobHandler";
    }

    @Override
    protected String getJobName() {
        return "TonglianV2ComplianceStatusJobHandler";
    }

    @Override
    protected List<PayBizApplyDO> queryPendingApplies(DirectJobParam param) {
        return queryComplianceAuditingApplies(param);
    }

    @Override
    protected void processApply(TonglianV2Context context) {
        processComplianceStatusCheck(context);
    }

    /**
     * 查询合规性审核中的申请记录
     */
    private List<PayBizApplyDO> queryComplianceAuditingApplies(DirectJobParam param) {
        // 查询状态为开通中，详细状态为合规性审核中的申请记录，按priority正序排列
        Long queryTime = param.getQueryTime();
        LocalDateTime start = LocalDateTime.now().minusSeconds(queryTime);
        return payBizApplyDAO.selectComplianceAuditingApplies(DevCodeConstants.TONGLIAN_V2_DEV_CODE, start, param.getBatchSize());
    }

    /**
     * 处理合规性审核状态检查
     */
    private void processComplianceStatusCheck(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        log.info("开始处理申请单合规性审核状态检查，applyId: {}, merchantSn: {}, detailStatus: {}",
                apply.getId(), apply.getMerchantSn(), apply.getDetailStatus());

        try {
            // 前置校验：获取商户参数和通联参数
            if (!validateAndPrepareContext(context)) {
                log.warn("前置校验失败，跳过处理，applyId: {}", apply.getId());
                delayPriority(apply);
                return;
            }

            // 调用通联接口查询合规性审核状态
            handleComplianceStatusQuery(context);

        } catch (Exception e) {
            log.error("处理申请单合规性审核状态检查异常，applyId: {}", apply.getId(), e);
            // 推迟处理时间
            delayPriority(apply);
        }
    }


    /**
     * 处理合规性审核状态查询
     */
    private void handleComplianceStatusQuery(TonglianV2Context context) {
        PayBizApplyDO apply = context.getApply();
        log.info("开始查询合规性审核状态，applyId: {}, merchantSn: {}", apply.getId(), apply.getMerchantSn());

        try {
            TongLianV2Param tongLianV2Param = context.getTongLianV2Param();
            // 查询合规性审核状态
            ContractResponse contractResponse = tongLianV2Service.queryComplianceStatus(apply.getMerchantSn(), tongLianV2Param);
            if (!contractResponse.isSuccess()) {
                log.error("查询合规性审核状态失败，applyId: {}, response: {}", apply.getId(), contractResponse.getMessage());
                delayPriority(apply);
                return;
            }

            ComplianceQueryResponse complianceResponse = JSON.parseObject(
                    JSON.toJSONString(contractResponse.getResponseParam()), ComplianceQueryResponse.class);

            // 处理不同的合规性审核状态
            handleComplianceStatusResponse(context, complianceResponse);

        } catch (Exception e) {
            log.error("查询合规性审核状态异常，applyId: {}", apply.getId(), e);
            delayPriority(apply);
        }
    }

    /**
     * 处理合规性审核状态响应
     */
    private void handleComplianceStatusResponse(TonglianV2Context context, ComplianceQueryResponse complianceResponse) {
        PayBizApplyDO apply = context.getApply();

        // TODO 如何区分审核中、审核成功、审核失败
        String cfstate = complianceResponse.getCfstate();
        String handlestate = complianceResponse.getHandlestate();
        if (("02".equals(cfstate) || "05".equals(cfstate)) && "0".equals(handlestate)) {
            // 审核成功
            transitionToNextStage(apply);
        } else if ("00".equals(cfstate)) {
            delayPriority(apply);
        } else {
            // 审核失败
            handleComplianceAuditFailed(context, complianceResponse);
        }
    }

    /**
     * 转换到下一阶段（启用中）
     */
    private void transitionToNextStage(PayBizApplyDO apply) {
        try {
            // 更新状态为启用中
            payBizApplyDAO.completeCurrentStageAndMoveToNext(apply);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.COMPLIANCE_AUDIT_PASS,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.PROCESSING, PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_PROCESS, null);

            log.info("合规性审核通过，转换到启用中状态，applyId: {}", apply.getId());

        } catch (Exception e) {
            log.error("转换到启用中状态失败，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 处理合规性审核失败
     */
    private void handleComplianceAuditFailed(TonglianV2Context context, ComplianceQueryResponse complianceResponse) {
        PayBizApplyDO apply = context.getApply();
        try {
            // 获取失败原因
            String failureReason = complianceResponse.getHandlemsg();
            String handledetail = complianceResponse.getHandledetail();

            // 调用风控接口进行文案转译 如果失败就先 delay 申请单
            boolean translatedResult = createContractSupplRecord(context, handledetail);
            if (translatedResult) {
                delayPriority(apply);
                return;
            }

            // 更新状态为合规性审核失败
            payBizApplyDAO.updateToComplianceFailed(apply, failureReason, handledetail);

            // 记录操作日志
            payBizApplyLogDAO.logOperationWithJsonDetail(
                    String.valueOf(apply.getId()),
                    PayBizApplyOperationEnum.COMPLIANCE_AUDIT_FAIL,
                    "SYSTEM",
                    "SYSTEM",
                    "SYSTEM",
                    PayBizApplyLogDetailBO.create(PayBizApplyStatusEnum.FAILED, PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED)
            );
            directStatusBiz.createOrUpdateDirectStatus(apply.getMerchantSn(), DevCodeConstants.TONGLIAN_V2_DEV_CODE, DirectStatus.STATUS_BIZ_FAIL, failureReason);

            log.info("合规性审核失败，更新状态完成，applyId: {}, reason: {}", apply.getId(), failureReason);

        } catch (Exception e) {
            log.error("处理合规性审核失败异常，applyId: {}", apply.getId(), e);
        }
    }

    /**
     * 调用风控接口进行文案转译
     */
    private boolean createContractSupplRecord(TonglianV2Context context, String handledetail) {
        try {
            PayBizApplyDO apply = context.getApply();
            // 字段转换
            Map<String, String> handleDetailMap = JSON.parseObject(handledetail, new TypeReference<Map<String, String>>() {
            });
            Map<String, String> tonglianV2ComplianceFieldMapping = applicationApolloConfig.getTonglianV2ComplianceFieldMapping();
            List<ContractSupplTransferReq.RejectInfo> rejectInfos = new ArrayList<>();
            for (Map.Entry<String, String> stringStringEntry : handleDetailMap.entrySet()) {
                if (!"1".equals(stringStringEntry.getValue())) {
                    TonglianV2ComplianceRejectEnum rejectEnum = TonglianV2ComplianceRejectEnum.findByFieldIdAndValue(stringStringEntry.getKey(), stringStringEntry.getValue());
                    ContractSupplTransferReq.RejectInfo rejectInfo = new ContractSupplTransferReq.RejectInfo();
                    String rejectSqbField = tonglianV2ComplianceFieldMapping.get(stringStringEntry.getKey());
                    if (WosaiStringUtils.isNotEmpty(rejectSqbField)) {
                        rejectInfo.setFieldKey(rejectEnum.getFieldId());
                        rejectInfo.setReason(rejectEnum.getDescription());
                        rejectInfos.add(rejectInfo);
                    }
                }
            }
            ContractSupplTransferReq contractSupplTransferReq = new ContractSupplTransferReq();
            contractSupplTransferReq.setMerchantSn(apply.getMerchantSn());
            contractSupplTransferReq.setContractType(AcquirerTypeEnum.TONG_LIAN_V2.getValue());
            contractSupplTransferReq.setProviderMerchantSn(context.getMerchantProviderParam().getPayMerchantId());
            contractSupplTransferReq.setRejectInfos(rejectInfos);
            contractSupplService.createContractSupplRecord(contractSupplTransferReq);
        } catch (Exception e) {
            log.error("{} 风控文案转译失败，原始原因: {}", context.getApply().getMerchantSn(), handledetail, e);
            return false;
        }
        return true;
    }

    /**
     * 推迟优先级处理时间（智能延迟策略）
     */
    private void delayPriority(PayBizApplyDO apply) {
        try {
            int delayMinutes = BASE_DELAY_MINUTES_1_3_DAYS + RANDOM.nextInt(RANDOM_DELAY_RANGE);
            LocalDateTime newPriority = LocalDateTime.now().plusMinutes(delayMinutes);
            payBizApplyDAO.updatePriority(apply, newPriority);

            log.debug("推迟处理时间，applyId: {}, delayMinutes: {}, newPriority: {}",
                    apply.getId(), delayMinutes, newPriority);
        } catch (Exception e) {
            log.error("推迟处理时间失败，applyId: {}", apply.getId(), e);
        }
    }


}
