package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 待生效状态
 */
@Slf4j
@Component
public class WaitingEffectiveState extends PayBizApplyState {

    public WaitingEffectiveState() {
        super(PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE,
                PayBizApplyEvent.ENABLE_REQUEST);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case ENABLE_REQUEST:
                // 启用请求 -> 启用中
                log.info("收到启用请求，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.ENABLING;

            default:
                log.warn("待生效状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        if (!validateCurrentState(context)) {
            return false;
        }

        // 可以添加其他业务条件检查
        // 例如：检查是否满足启用条件等

        return true;
    }
}
