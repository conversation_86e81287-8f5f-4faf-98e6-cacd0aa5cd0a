package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 待生效状态
 * 对应状态流转图中的 WAITING_EFFECTIVE
 */
@Slf4j
@Component
public class WaitingEffectiveState extends PayBizApplyState {

    public WaitingEffectiveState() {
        super(PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE,
                PayBizApplyEvent.MANUAL_INTERVENTION);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case MANUAL_INTERVENTION:
                // 手动干预 -> 启用中 或 开通成功
                String action = context.getParameter("action", String.class);
                if ("enable".equals(action)) {
                    log.info("手动启用，申请单ID: {}", context.getApply().getId());
                    return PayBizApplyDetailStatusEnum.ENABLING;
                } else if ("skip_enable".equals(action)) {
                    log.info("手动不启用，申请单ID: {}", context.getApply().getId());
                    return PayBizApplyDetailStatusEnum.OPEN_SUCCESS;
                } else {
                    log.warn("未知的手动干预动作: {}, 申请单ID: {}", action, context.getApply().getId());
                    return null;
                }

            default:
                log.warn("待生效状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入待生效状态，申请单ID: {}", context.getApply().getId());
    }

    @Override
    protected boolean canHandleInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        if (!validateCurrentState(context)) {
            return false;
        }

        // 可以添加其他业务条件检查
        // 例如：检查是否满足启用条件等

        return true;
    }
}
