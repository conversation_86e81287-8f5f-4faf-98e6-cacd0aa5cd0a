package com.wosai.upay.job.statemachine.paybizapply;

import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务申请事件枚举
 * 基于状态流转图定义的所有事件，与PayBizApplyOperationEnum对应
 */
@Getter
@AllArgsConstructor
public enum PayBizApplyEvent {

    // 创建申请单
    CREATE_APPLY("CREATE_APPLY", "创建申请单", PayBizApplyOperationEnum.CREATE_APPLY),

    // 进件审核相关事件
    ENTRY_AUDIT_PASS("ENTRY_AUDIT_PASS", "进件审核通过", PayBizApplyOperationEnum.ENTRY_AUDIT_PASS),
    ENTRY_AUDIT_FAIL("ENTRY_AUDIT_FAIL", "进件审核失败", PayBizApplyOperationEnum.ENTRY_AUDIT_FAIL),

    // 重新提交申请
    RESUBMIT_APPLY("RESUBMIT_APPLY", "重新提交申请", PayBizApplyOperationEnum.RESUBMIT_APPLY),

    // 签约相关事件
    SIGN_SUCCESS("SIGN_SUCCESS", "签约成功", PayBizApplyOperationEnum.SIGN_SUCCESS),
    LEGAL_SIGN_FAIL("LEGAL_SIGN_FAIL", "法人签约失败", PayBizApplyOperationEnum.LEGAL_SIGN_FAIL),
    SETTLEMENT_SIGN_FAIL("SETTLEMENT_SIGN_FAIL", "结算人签约失败", PayBizApplyOperationEnum.SETTLEMENT_SIGN_FAIL),
    SIGN_URL_EXPIRED("SIGN_URL_EXPIRED", "签约链接过期", PayBizApplyOperationEnum.SIGN_URL_EXPIRED),
    RESIGN("RESIGN", "重新发送签约链接", PayBizApplyOperationEnum.RESIGN),

    // 风控审核相关事件
    RISK_CONTROL_SUCCESS("RISK_CONTROL_SUCCESS", "风控审核通过", PayBizApplyOperationEnum.SYSTEM_AUTO_PROCESS),
    
    // 合规审核相关事件
    COMPLIANCE_AUDIT_PASS("COMPLIANCE_AUDIT_PASS", "合规审核通过", PayBizApplyOperationEnum.COMPLIANCE_AUDIT_PASS),
    COMPLIANCE_AUDIT_FAIL("COMPLIANCE_AUDIT_FAIL", "合规审核失败", PayBizApplyOperationEnum.COMPLIANCE_AUDIT_FAIL),

    // 启用相关事件
    MANUAL_INTERVENTION("MANUAL_INTERVENTION", "手动干预", PayBizApplyOperationEnum.MANUAL_INTERVENTION),
    ENABLED_FAILED("ENABLED_FAILED", "启用失败", PayBizApplyOperationEnum.ENABLED_FAILED),
    OPEN_SUCCESS("OPEN_SUCCESS", "开通成功", PayBizApplyOperationEnum.OPEN_SUCCESS);

    /**
     * 事件代码
     */
    private final String code;

    /**
     * 事件描述
     */
    private final String description;

    /**
     * 对应的操作类型
     */
    private final PayBizApplyOperationEnum operationType;

    /**
     * 根据代码获取事件
     */
    public static PayBizApplyEvent getByCode(String code) {
        for (PayBizApplyEvent event : values()) {
            if (event.getCode().equals(code)) {
                return event;
            }
        }
        return null;
    }

    /**
     * 根据操作类型获取事件
     */
    public static PayBizApplyEvent getByOperationType(PayBizApplyOperationEnum operationType) {
        for (PayBizApplyEvent event : values()) {
            if (event.getOperationType() == operationType) {
                return event;
            }
        }
        return null;
    }
}
