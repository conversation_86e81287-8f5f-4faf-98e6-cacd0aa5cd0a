package com.wosai.upay.job.statemachine.paybizapply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务申请事件枚举
 * 定义触发状态转换的所有事件
 */
@Getter
@AllArgsConstructor
public enum PayBizApplyEvent {

    // 进件相关事件
    CONTRACT_AUDIT_SUCCESS("CONTRACT_AUDIT_SUCCESS", "进件审核成功"),
    CONTRACT_AUDIT_FAIL("CONTRACT_AUDIT_FAIL", "进件审核失败"),

    // 法人签约相关事件
    LEGAL_SIGN_SUCCESS("LEGAL_SIGN_SUCCESS", "法人签约成功"),
    LEGAL_SIGN_FAIL("LEGAL_SIGN_FAIL", "法人签约失败"),
    LEGAL_SIGN_EXPIRE("LEGAL_SIGN_EXPIRE", "法人签约过期"),

    // 结算人签约相关事件
    SETTLEMENT_SIGN_SUCCESS("SETTLEMENT_SIGN_SUCCESS", "结算人签约成功"),
    SETTLEMENT_SIGN_FAIL("SETTLEMENT_SIGN_FAIL", "结算人签约失败"),
    SETTLEMENT_SIGN_EXPIRE("SETTLEMENT_SIGN_EXPIRE", "结算人签约过期"),

    // 风控审核相关事件
    RISK_AUDIT_SUCCESS("RISK_AUDIT_SUCCESS", "风控审核成功"),
    RISK_AUDIT_FAIL("RISK_AUDIT_FAIL", "风控审核失败"),

    // 合规补录相关事件
    COMPLIANCE_AUDIT_SUCCESS("COMPLIANCE_AUDIT_SUCCESS", "合规审核成功"),
    COMPLIANCE_AUDIT_FAIL("COMPLIANCE_AUDIT_FAIL", "合规审核失败"),
    SUPPLEMENT_REQUIRED("SUPPLEMENT_REQUIRED", "需要补录"),

    // 启用相关事件
    ENABLE_REQUEST("ENABLE_REQUEST", "启用请求"),
    ENABLE_SUCCESS("ENABLE_SUCCESS", "启用成功"),
    ENABLE_FAIL("ENABLE_FAIL", "启用失败"),

    // 通用事件
    ROLLBACK("ROLLBACK", "回退操作"),
    RETRY("RETRY", "重试操作"),
    MANUAL_INTERVENTION("MANUAL_INTERVENTION", "人工干预");

    /**
     * 事件代码
     */
    private final String code;

    /**
     * 事件描述
     */
    private final String description;

    /**
     * 根据代码获取事件
     */
    public static PayBizApplyEvent getByCode(String code) {
        for (PayBizApplyEvent event : values()) {
            if (event.getCode().equals(code)) {
                return event;
            }
        }
        return null;
    }
}
