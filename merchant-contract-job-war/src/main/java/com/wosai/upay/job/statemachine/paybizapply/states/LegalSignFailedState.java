package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法人签约失败状态
 */
@Slf4j
@Component
public class LegalSignFailedState extends PayBizApplyState {

    public LegalSignFailedState() {
        super(PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED,
                PayBizApplyEvent.ROLLBACK,
                PayBizApplyEvent.RETRY);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case ROLLBACK:
            case RETRY:
                // 回退到待法人签约，重新签约
                log.info("法人签约失败回退，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN;

            default:
                log.warn("法人签约失败状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    public boolean isFinalState() {
        return true; // 这是一个失败的终态
    }
}
