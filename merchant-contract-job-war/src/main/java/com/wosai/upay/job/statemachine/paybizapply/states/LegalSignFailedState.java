package com.wosai.upay.job.statemachine.paybizapply.states;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法人签约失败状态
 * 对应状态流转图中的 LEGAL_SIGN_FAILED
 */
@Slf4j
@Component
public class LegalSignFailedState extends PayBizApplyState {

    public LegalSignFailedState() {
        super(PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED,
                PayBizApplyEvent.RESUBMIT_APPLY);
    }

    @Override
    protected PayBizApplyDetailStatusEnum handleEventInternal(PayBizApplyEvent event, PayBizApplyContext context) {
        switch (event) {
            case RESUBMIT_APPLY:
                // 重新提交申请 -> 待法人签约
                log.info("法人签约失败重新提交申请，申请单ID: {}", context.getApply().getId());
                return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN;

            default:
                log.warn("法人签约失败状态不支持事件: {}", event);
                return null;
        }
    }

    @Override
    protected void onEnterInternal(PayBizApplyContext context) {
        log.info("进入法人签约失败状态，申请单ID: {}", context.getApply().getId());
        
        // 更新失败结果
        String failResult = context.getFailResult();
        if (failResult != null) {
            context.getApply().updateResult(failResult);
        }
    }

    @Override
    public boolean isFinalState() {
        return true; // 这是一个失败的终态
    }
}
