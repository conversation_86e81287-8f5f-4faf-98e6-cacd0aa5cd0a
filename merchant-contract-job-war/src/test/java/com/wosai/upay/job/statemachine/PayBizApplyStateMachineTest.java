package com.wosai.upay.job.statemachine;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.statemachine.core.StateMachineResult;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyContext;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyEvent;
import com.wosai.upay.job.statemachine.paybizapply.PayBizApplyStateMachine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 支付业务申请状态机单元测试
 */
@ExtendWith(MockitoExtension.class)
class PayBizApplyStateMachineTest {

    private PayBizApplyStateMachine stateMachine;
    private PayBizApplyDO testApply;

    @BeforeEach
    void setUp() {
        stateMachine = new PayBizApplyStateMachine();
        // 手动初始化状态机（在实际环境中由Spring自动完成）
        // stateMachine.init();

        // 创建测试申请单
        testApply = new PayBizApplyDO();
        testApply.setId(1L);
        testApply.setMerchantSn("TEST_MERCHANT");
        testApply.setDevCode("TONGLIAN_V2");
        testApply.setStatus(PayBizApplyStatusEnum.PROCESSING.getCode());
        testApply.setDetailStatus(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode());
    }

    @Test
    void testContractAuditSuccess() {
        // 准备测试数据
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 执行状态转换
        StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.CONTRACT_AUDIT_SUCCESS,
                context
        );

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING, result.getFromState());
        assertEquals(PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN, result.getToState());
    }

    @Test
    void testContractAuditFail() {
        // 准备测试数据
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 执行状态转换
        StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.CONTRACT_AUDIT_FAIL,
                context
        );

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING, result.getFromState());
        assertEquals(PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED, result.getToState());
    }

    @Test
    void testInvalidStateTransition() {
        // 准备测试数据
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 尝试无效的状态转换
        StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.LEGAL_SIGN_SUCCESS, // 进件审核中状态不支持法人签约成功事件
                context
        );

        // 验证结果
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
    }

    @Test
    void testCanTransition() {
        // 准备测试数据
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 测试可以转换的情况
        assertTrue(stateMachine.canTransition(
                PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.CONTRACT_AUDIT_SUCCESS,
                context
        ));

        // 测试不可以转换的情况
        assertFalse(stateMachine.canTransition(
                PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.LEGAL_SIGN_SUCCESS,
                context
        ));
    }

    @Test
    void testLegalSignFlow() {
        // 测试法人签约流程
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 1. 法人签约成功
        StateMachineResult<PayBizApplyDetailStatusEnum> result1 = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN,
                PayBizApplyEvent.LEGAL_SIGN_SUCCESS,
                context
        );
        assertTrue(result1.isSuccess());
        assertEquals(PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN, result1.getToState());

        // 2. 法人签约失败
        StateMachineResult<PayBizApplyDetailStatusEnum> result2 = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN,
                PayBizApplyEvent.LEGAL_SIGN_FAIL,
                context
        );
        assertTrue(result2.isSuccess());
        assertEquals(PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED, result2.getToState());

        // 3. 法人签约过期
        StateMachineResult<PayBizApplyDetailStatusEnum> result3 = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN,
                PayBizApplyEvent.LEGAL_SIGN_EXPIRE,
                context
        );
        assertTrue(result3.isSuccess());
        assertEquals(PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED, result3.getToState());
    }

    @Test
    void testRollback() {
        // 测试状态回退
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 从待法人签约状态回退到进件审核中
        StateMachineResult<PayBizApplyDetailStatusEnum> result = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN,
                PayBizApplyEvent.ROLLBACK,
                context
        );

        assertTrue(result.isSuccess());
        assertEquals(PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN, result.getFromState());
        assertEquals(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING, result.getToState());
    }

    @Test
    void testCompleteFlow() {
        // 测试完整的成功流程
        PayBizApplyContext context = PayBizApplyContext.create(testApply);

        // 1. 进件审核成功
        StateMachineResult<PayBizApplyDetailStatusEnum> result1 = stateMachine.fireEvent(
                PayBizApplyDetailStatusEnum.CONTRACT_AUDITING,
                PayBizApplyEvent.CONTRACT_AUDIT_SUCCESS,
                context
        );
        assertTrue(result1.isSuccess());

        // 2. 法人签约成功
        StateMachineResult<PayBizApplyDetailStatusEnum> result2 = stateMachine.fireEvent(
                result1.getToState(),
                PayBizApplyEvent.LEGAL_SIGN_SUCCESS,
                context
        );
        assertTrue(result2.isSuccess());

        // 3. 结算人签约成功
        StateMachineResult<PayBizApplyDetailStatusEnum> result3 = stateMachine.fireEvent(
                result2.getToState(),
                PayBizApplyEvent.SETTLEMENT_SIGN_SUCCESS,
                context
        );
        assertTrue(result3.isSuccess());

        // 验证最终状态
        assertEquals(PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING, result3.getToState());
    }
}
